{"name": "@olading/olading-platform-boss-frontend", "version": "0.1.0", "main": "admin/index.ts", "dependencies": {"@lanmaoly/admin-framework": " 1.0.14", "axios": "^0.19.0", "crypto-js": "^4.2.0", "react-quill": "^1.3.5"}, "homepage": "/__PUBLIC_PATH_PLACEHOLDER__", "scripts": {"start": "react-app-rewired start", "build": "set PUBLIC_URL=//cloud.bankofchina.com/gd/hrsaas/platform-boss/&&react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/history": "^4.7.2", "@types/jest": "24.0.18", "@types/lodash": "^4.14.137", "@types/mockjs": "^1.0.2", "@types/node": "12.7.2", "@types/react": "16.9.2", "@types/react-dom": "16.9.0", "@types/react-resizable": "^1.7.1", "@types/react-router": "^5.0.3", "@types/react-router-dom": "^4.3.4", "axios-mock-adapter": "^1.17.0", "customize-cra": "^0.5.0", "http-proxy-middleware": "^0.19.1", "less": "^3.10.3", "less-loader": "^5.0.0", "mockjs": "^1.0.1-beta3", "react-app-rewired": "^2.1.8", "react-scripts": "^3.4.1", "typescript": "3.7.5", "webpack": "4.42.0"}}