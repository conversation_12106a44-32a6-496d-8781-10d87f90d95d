{"compilerOptions": {"target": "es2018", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "strictNullChecks": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react", "experimentalDecorators": true, "noImplicitAny": false}, "include": ["src"]}