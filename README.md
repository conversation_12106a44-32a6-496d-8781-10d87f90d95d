# 项目介绍

阿拉丁boss后台

# 安装依赖
```bash
npm install
```

# 启动项目
```bash
npm run start
```

# 项目配置

## 本地配置

本地配置在`public\env.js`

## 远程配置

需要在bitbucket中修改 `env`的变量，

![image-20250620105852309](.\docs\imgs\bitbucket配置.png)

 在 `script\env.template` 中
 使用在`bitbucket`中定义的变量


# 配置说明
```js
window.env = {
  // api接口地址 
  apiPath:"https://46-qa.olading.com/gd/hrsaas/webapi/api/platform-boss",
  // boss后台登录地址（用于获取token）
  bossLogin: "https://46-qa.olading.com/gd/hrsaas/boss/lanmao-boss-app/login",
  // base地址
  staticPath: "/gd/hrsaas/platform-boss/",
}
```