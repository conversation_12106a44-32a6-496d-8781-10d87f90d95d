
import { ApplicationMenu, ApplicationViewFactory } from "@lanmaoly/admin-framework"

const AUTHORITY_TENANT = "authz.tenant"
const AUTHORITY_AUTHORIZATIONRECORD = "authz.authorizationrecord"
const AUTHORITY_AUTHORIZATIONSCOPE = "authz.authorization.scope"
const AUTHORITY_SERVICEPROVIDER = "authz.authorityserviceprocider"
const AUTHORITY_TENANTCUSTOMER = "authz.tenant.customer"
const AUTHORITY_ROLE = "authz.role"
const PROMOTION_PRODUCT = "promotion.product"
const POTENTIAL_CUSTOMER = "potential.customer"
const PLATFORM_PROTOCOL = "platform.protocol"
const PLATFORM_SMS_CONFIG = "platfrom.sms.config"
const PLATFORM_SMS_RECORD = "platfrom.sms.send.record"
const PLATFORM_AGENT = "platfrom.agent"
const PLATFORM_APPLICATION_GROUP = "platfrom.application.group"
const MERCHANT_WEBSITE_CONFIG = "merchant.website.config"

const AUTHORITY_ARCHIVE = "ola.plat.archive"
const AUTHORITY_MAIL = "ola.plat.mail"
const AUTHORITY_SMS = "ola.plat.sms"
const AUTHORITY_MERCHANT_PRIVILEGE = "ola.plat.mer.pri"

const menus: ApplicationMenu[] = [{
  title: '商户',
  children: [{
    title: '商户管理',
    path: '/merchant/merchant'
  }]
}, {
  title: '认证授权',
  children: [{
    title: '租户管理',
    path: '/authz/tenant',
    authority: [AUTHORITY_TENANT]
  }, {
    title: '租户角色管理',
    path: '/authz/role',
    authority: [AUTHORITY_ROLE]
  }, {
    title: '授权管理',
    path: '/authz/authorizationrecord',
    authority: [AUTHORITY_AUTHORIZATIONRECORD]
  },{
    title: '钉签约授权管理',
    path: '/authz/sign-auth',
    authority: [AUTHORITY_AUTHORIZATIONRECORD]
  },  {
    title: '授权项管理',
    path: '/authz/authorizationscope',
    authority: [AUTHORITY_AUTHORIZATIONSCOPE]
  }, {
    title: '租户客户管理',
    path: '/authz/tenantcustomer',
    authority: [AUTHORITY_TENANTCUSTOMER]
  }, {
    title: '服务商管理',
    path: '/authz/serviceProvider',
    authority: [AUTHORITY_SERVICEPROVIDER]
  },]
}, {
  title: '本地文档管理',
  children: [{
    title: '本地文档信息',
    path: '/archive/info',
    authority: [AUTHORITY_ARCHIVE]
  }]
},
{
  title: '推广管理',
  children: [{
    title: '意向客户管理',
    path: '/potential/customer',
    authority: [POTENTIAL_CUSTOMER]
  }, {
    title: '产品管理',
    path: '/promotion/product',
    authority: [PROMOTION_PRODUCT]
  }]
},
{
  title: '系统设置',
  children: [{
    title: '权限数据',
    path: '/authority/data'
  },{
    title: '实名认证缓存',
    path: '/realName/cache'
  }]
},
{
  title: '角色权限管理',
  children: [{
    title: '角色管理',
    path: '/roleprivilege/roleManager',
    authority: [AUTHORITY_MERCHANT_PRIVILEGE]
  }, {
    title: '业务角色管理',
    path: '/roleprivilege/businessRole',
    authority: [AUTHORITY_MERCHANT_PRIVILEGE]
  }, {
    title: '商户角色管理',
    path: '/roleprivilege/merchantRoleManager',
    authority: [AUTHORITY_MERCHANT_PRIVILEGE]
  }]
},
{
  title: '协议管理',
  children: [{
    title: '协议列表',
    path: '/protocol/list',
    authority: [PLATFORM_PROTOCOL]
  }]
},
{
  title: '短信服务',
  children: [{
    title: '短信配置',
    path: '/sms/config',
    authority: [PLATFORM_SMS_CONFIG]
  }, {
    title: '短信发送记录',
    path: '/sms/sendRecord',
    authority: [PLATFORM_SMS_RECORD]
  }]
},
{
  title: '短信平台',
  children: [{
    title: '短信模板',
    path: '/sms/template',
    authority: [AUTHORITY_SMS]
  }, {
    title: '短信业务',
    path: '/sms/business',
    authority: [AUTHORITY_SMS]
  }, {
    title: '短信通道',
    path: '/sms/channel',
    authority: [AUTHORITY_SMS]
  }, {
    title: '懒猫短信通道',
    path: '/sms/lanmaolyconfig',
    authority: [AUTHORITY_SMS]
  }]
},
{
  title: '邮件服务',
  children: [{
    title: '邮件账号',
    path: '/mail/account',
    authority: [AUTHORITY_MAIL]
  }, {
    title: '邮件模板',
    path: '/mail/template',
    authority: [AUTHORITY_MAIL]
  }, {
    title: '邮件发送记录',
    path: '/mail/record',
    authority: [AUTHORITY_MAIL]
  }]
},
{
  title: '资质方管理',
  children: [{
    title: '资质方品牌信息',
    path: '/agent/info',
    authority: [PLATFORM_AGENT]
  }]
},
{
  title: '应用分组管理',
  children: [{
    title: '应用分组信息',
    path: '/application/group',
    authority: [PLATFORM_APPLICATION_GROUP]
  }]
},
{
  title: '网站配置',
  children: [{
    title: '网站配置信息',
    path: '/website/config',
    authority: [MERCHANT_WEBSITE_CONFIG]
  },
  {
    title: '小程序配置信息',
    path: '/website/configMp',
    authority: [MERCHANT_WEBSITE_CONFIG]
  },
  {
    title: '商户入网配置',
    path: '/website/configMn',
    authority: [MERCHANT_WEBSITE_CONFIG]
  }
  ]
},
// 上线隐藏操作日志模块
{
  title:'操作日志',
  children:[
    {
      title:'操作记录',
      path:'/operation/log'
    },
    {
      title:'导出失败记录',
      path:'/operation/defeat'
    },
    {
      title:'系统日志诊断',
      path:'/operation/diagnostic'
    }
  ]
},
{
  title:'专区管理',
  children:[
    {
      title:'服务管理',
      path:'/zone/service'
    },
    {
      title:'布局管理',
      path:'/zone/layout'
    },
    {
      title:'商户管理',
      path:'/thrid/merchant'
    },
    {
      title:'申请管理',
      path:'/thrid/applies'
    }
  ]
}
]


const factory: ApplicationViewFactory[] = [{
  path: "/profile",
  create: (props) => {
    return {
      title: "个人信息",
      render: () => import("./view/UserProfileView")
    }
  }
}, {
  path: "/dashboard",
  create: (props) => {
    return {
      title: "首页",
      render: () => import("./view/DashboardView")
    }
  }
}, {
  path: "/authz/tenant",
  create: (props) => {
    return {
      title: "租户管理",
      authority: [AUTHORITY_TENANT],
      render: () => import("./view/data/tenant/TenantListView")
    }
  }
}, {
  path: "/authz/tenant/add",
  create: (props) => {
    return {
      title: "添加租户",
      authority: [AUTHORITY_TENANT],
      render: () => import("./view/data/tenant/TenantEditView")
    }
  }
}, {
  path: "/authz/tenant/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑租户${match.params.id}`,
      authority: [AUTHORITY_TENANT],
      render: () => import("./view/data/tenant/TenantEditView")
    }
  }
}, {
  path: "/authz/tenant/developer/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `开发者账号${match.params.id}`,
      authority: [AUTHORITY_TENANT],
      render: () => import("./view/data/tenant/DeveloperListView")
    }
  }
}, {
  path: "/authz/tenant/:tenant/developer/add",
  create: (props) => {
    return {
      title: `创建开发者账号`,
      authority: [AUTHORITY_TENANT],
      render: () => import("./view/data/tenant/DeveloperEditView")
    }
  }
}, {
  path: "/authz/developer/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑开发者账号${match.params.id}`,
      authority: [AUTHORITY_TENANT],
      render: () => import("./view/data/tenant/DeveloperEditView")
    }
  }
}]

//系统权限
factory.push(...[
  {
    path: "/authority/data",
    create: (props) => {
        return {
        title: "权限数据",
        render: () => import("./view/data/authority/DataView")
        }
    }
  },
  {
    path: "/realName/cache",
    create: (props) => {
      return {
        title: "实名认证缓存",
        render: () => import("./view/auth/SetAuthCacheView"),
      };
    },
  },
])


//授权管理
factory.push(...[{
  path: "/authz/authorizationrecord",
  create: (props) => {
    return {
      title: "授权管理",
      authority: [AUTHORITY_AUTHORIZATIONRECORD],
      render: () => import("./view/data/authorizationrecord/AuthorizationRecordListView")
    }
  }
}, {
  path: "/authz/authorizationrecord/add",
  create: (props) => {
    return {
      title: "添加授权信息",
      authority: [AUTHORITY_AUTHORIZATIONRECORD],
      render: () => import("./view/data/authorizationrecord/AuthorizationRecordEditView")
    }
  }
}, {
  path: "/authz/authorizationrecord/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑授权信息${match.params.id}`,
      authority: [AUTHORITY_AUTHORIZATIONRECORD],
      render: () => import("./view/data/authorizationrecord/AuthorizationRecordEditView")
    }
  }
  }])
// 钉签约授权管理
factory.push(...[{
  path: "/authz/sign-auth",
  create: (props) => {
    return {
      title: "钉签约授权管理",
      authority: [AUTHORITY_AUTHORIZATIONRECORD],
      render: () => import("./view/data/signAuth/SignAuthView")
    }
  }
}])

// 租户角色管理
factory.push(...[{
  path: "/authz/role",
  create: (props) => {
    return {
      title: "租户角色管理",
      authority: [AUTHORITY_ROLE],
      render: () => import("./view/data/role/TenantRoleListView")
    }
  }
}, {
  path: "/authz/role/add",
  create: (props) => {
    return {
      title: "添加租户角色",
      authority: [AUTHORITY_ROLE],
      render: () => import("./view/data/role/TenantRoleEditView")
    }
  }
}, {
  path: "/authz/role/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑租户角色${match.params.id}`,
      authority: [AUTHORITY_ROLE],
      render: () => import("./view/data/role/TenantRoleEditView")
    }
  }
}])

//授权项管理
factory.push(...[{
  path: "/authz/authorizationscope",
  create: (props) => {
    return {
      title: "授权项管理",
      authority: [AUTHORITY_AUTHORIZATIONSCOPE],
      render: () => import("./view/data/authorizationscope/AuthorizationScopeListView")
    }
  }
}, {
  path: "/authz/authorizationscope/add",
  create: (props) => {
    return {
      title: "添加授权项信息",
      authority: [AUTHORITY_AUTHORIZATIONSCOPE],
      render: () => import("./view/data/authorizationscope/AuthorizationScopeEditView")
    }
  }
}, {
  path: "/authz/authorizationscope/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑授权项信息${match.params.id}`,
      authority: [AUTHORITY_AUTHORIZATIONSCOPE],
      render: () => import("./view/data/authorizationscope/AuthorizationScopeEditView")
    }
  }
}])

//租户客户管理
factory.push(...[{
  path: "/authz/tenantcustomer",
  create: (props) => {
    return {
      title: "租户客户管理",
      authority: [AUTHORITY_TENANTCUSTOMER],
      render: () => import("./view/data/tenantcustomer/TenantCustomerListView")
    }
  }
}, {
  path: "/authz/tenantcustomer/add",
  create: (props) => {
    return {
      title: "添加租户客户管理",
      authority: [AUTHORITY_TENANTCUSTOMER],
      render: () => import("./view/data/tenantcustomer/TenantCustomerEditView")
    }
  }
}, {
  path: "/authz/tenantcustomer/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑租户客户管理${match.params.id}`,
      authority: [AUTHORITY_TENANTCUSTOMER],
      render: () => import("./view/data/tenantcustomer/TenantCustomerEditView")
    }
  }
}])

//服务商管理
factory.push(...[{
  path: "/authz/serviceProvider",
  create: (props) => {
    return {
      title: "服务商管理列表",
      authority: [AUTHORITY_SERVICEPROVIDER],
      render: () => import("./view/data/serviceprovider/ApplicationServerProviderListView")
    }
  }
}, {
  path: "/authz/serviceProvider/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加服务商管理信息`,
      authority: [AUTHORITY_SERVICEPROVIDER],
      render: () => import("./view/data/serviceprovider/ApplicationServerProviderEditView")
    }
  }
}, {
  path: "/authz/serviceProvider/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑服务商管理信息${match.params.id}`,
      authority: [AUTHORITY_SERVICEPROVIDER],
      render: () => import("./view/data/serviceprovider/ApplicationServerProviderEditView")
    }
  }
}])

// 推广管理
factory.push(...[{
  path: "/potential/customer",
  create: (props) => {
    return {
      title: "意向客户管理",
      authority: [POTENTIAL_CUSTOMER],
      render: () => import("./view/data/promotion/PotentialCustomerListView")
    }
  }
}, {
  path: "/potential/customer/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑意向客户${match.params.id}`,
      authority: [POTENTIAL_CUSTOMER],
      render: () => import("./view/data/promotion/PotentialCustomerEditView")
    }
  }
}, {
  path: "/promotion/product",
  create: (props) => {
    return {
      title: "产品管理",
      authority: [PROMOTION_PRODUCT],
      render: () => import("./view/data/promotion/PromotionProductListView")
    }
  }
}, {
  path: "/promotion/product/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑产品${match.params.id}`,
      authority: [PROMOTION_PRODUCT],
      render: () => import("./view/data/promotion/PromotionProductEditView")
    }
  }
}])

// 商户管理
factory.push(...[{
  path: "/merchant/merchant",
  create: (props) => {
    return {
      title: "商户管理",
      render: () => import("./view/data/merchant/MerchantListView")
    }
  }
}])



// 协议管理
factory.push(...[{
  path: "/protocol/list",
  create: (props) => {
    return {
      title: "协议管理",
      render: () => import("./view/data/protocol/ProtocolListView")
    }
  }
}, {
  path: "/protocol/signerList/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `签署人列表${match.params.id}`,
      authority: [PLATFORM_PROTOCOL],
      render: () => import("./view/data/protocol/ProtocolSignerListView")
    }
  }
}, {
  path: "/protocol/attachmentList/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `附件列表${match.params.id}`,
      authority: [PLATFORM_PROTOCOL],
      render: () => import("./view/data/protocol/ProtocoAttachmentListView")
    }
  }
}])




// 商户管理
factory.push(...[{
  path: "/merchant/merchant",
  create: (props) => {
    return {
      title: "商户管理",
      render: () => import("./view/data/merchant/MerchantListView")
    }
  }
}])


// 短信服务
factory.push(...[{
  path: "/sms/config",
  create: (props) => {
    return {
      title: "短信配置",
      authority: [PLATFORM_SMS_CONFIG],
      render: () => import("./view/data/sms/SmsConfigListView")
    }
  }
}, {
  path: "/sms/config/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加短信配置`,
      authority: [PLATFORM_SMS_CONFIG],
      render: () => import("./view/data/sms/SmsConfigEditView")
    }
  }
}, {
  path: "/sms/config/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑短信配置${match.params.id}`,
      authority: [PLATFORM_SMS_CONFIG],
      render: () => import("./view/data/sms/SmsConfigEditView")
    }
  }
}, {
  path: "/sms/sendRecord",
  create: (props) => {
    return {
      title: "短信发送记录",
      authority: [PLATFORM_SMS_RECORD],
      render: () => import("./view/data/sms/SmsSendRecordListView")
    }
  }
}])



// 短信平台
factory.push(...[{
  path: "/sms/template",
  create: (props) => {
    return {
      title: "短信模板",
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsTemplateListView")
    }
  }
}, {
  path: "/sms/template/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加短信模板`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsTemplateEditView")
    }
  }
}, {
  path: "/sms/template/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑短信模板${match.params.id}`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsTemplateEditView")
    }
  }
}, {
  path: "/sms/business",
  create: (props) => {
    return {
      title: "短信业务",
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsBusinessListView")
    }
  }
}, {
  path: "/sms/business/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加短信业务`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsBusinessEditView")
    }
  }
}, {
  path: "/sms/business/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑短信业务${match.params.id}`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsBusinessEditView")
    }
  }
}, {
  path: "/sms/channel",
  create: (props) => {
    return {
      title: "短信通道",
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsChannelListView")
    }
  }
}, {
  path: "/sms/channel/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加短信通道`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsChannelEditView")
    }
  }
}, {
  path: "/sms/channel/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑短信通道${match.params.id}`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsChannelEditView")
    }
  }
}, {
  path: "/sms/lanmaolyconfig",
  create: (props) => {
    return {
      title: "懒猫短信通道",
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsLanmaolyListView")
    }
  }
}, {
  path: "/sms/lanmaolyconfig/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加短信通道`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsLanmaolyEditView")
    }
  }
}, {
  path: "/sms/lanmaolyconfig/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑短信通道${match.params.id}`,
      authority: [AUTHORITY_SMS],
      render: () => import("./view/data/sms/SmsLanmaolyEditView")
    }
  }
}])


// 邮件服务
factory.push(...[{
  path: "/mail/account",
  create: (props) => {
    return {
      title: "邮件账号",
      render: () => import("./view/data/mail/MailAccountListView")
    }
  }
}, {
  path: "/mail/account/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加邮件账号`,
      render: () => import("./view/data/mail/MailAccountEditView")
    }
  }
}, {
  path: "/mail/account/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑邮件账号${match.params.id}`,
      render: () => import("./view/data/mail/MailAccountEditView")
    }
  }
}, {
  path: "/mail/template",
  create: (props) => {
    return {
      title: "邮件模板",
      render: () => import("./view/data/mail/MailTemplateListView")
    }
  }
}, {
  path: "/mail/template/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加邮件模板`,
      render: () => import("./view/data/mail/MailTemplateEditView")
    }
  }
}, {
  path: "/mail/template/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑邮件模板${match.params.id}`,
      render: () => import("./view/data/mail/MailTemplateEditView")
    }
  }
}, {
  path: "/mail/record",
  create: (props) => {
    return {
      title: "邮件发送记录",
      render: () => import("./view/data/mail/SendMailRecordListView")
    }
  }
}])


// 资质方管理
factory.push(...[{
  path: "/agent/info",
  create: (props) => {
    return {
      title: "资质方信息列表",
      authority: [PLATFORM_AGENT],
      render: () => import("./view/data/agent/AgentListView")
    }
  }
}, {
  path: "/agent/info/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加资质方信息`,
      authority: [PLATFORM_AGENT],
      render: () => import("./view/data/agent/AgentEditView")
    }
  }
}, {
  path: "/agent/info/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑资质方信息${match.params.id}`,
      authority: [PLATFORM_AGENT],
      render: () => import("./view/data/agent/AgentEditView")
    }
  }
}])

// 应用分组管理
factory.push(...[{
  path: "/application/group",
  create: (props) => {
    return {
      title: "应用分组列表",
      authority: [PLATFORM_APPLICATION_GROUP],
      render: () => import("./view/data/application/ApplicationGroupListView")
    }
  }
}, {
  path: "/application/group/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加应用分组`,
      authority: [PLATFORM_APPLICATION_GROUP],
      render: () => import("./view/data/application/ApplicationGroupEditView")
    }
  }
}, {
  path: "/application/group/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑应用分组${match.params.id}`,
      authority: [PLATFORM_APPLICATION_GROUP],
      render: () => import("./view/data/application/ApplicationGroupEditView")
    }
  }
}])

// 本地文档管理
factory.push(...[{
  path: "/archive/info",
  create: (props) => {
    return {
      title: "本地文档列表",
      authority: [AUTHORITY_ARCHIVE],
      render: () => import("./view/data/archive/ArchiveListView")
    }
  }
}, {
  path: "/archive/info/deleteBatches",
  create: (props) => {
    const { match } = props
    return {
      title: `批量删除文档`,
      authority: [AUTHORITY_ARCHIVE],
      render: () => import("./view/data/archive/AgentDeleteBatchesView")
    }
  }
}])

// 角色权限管理
factory.push(...[{
  path: "/roleprivilege/businessRole",
  create: (props) => {
    const { match } = props
    return {
      title: `业务角色管理`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/BusinessRoleListView")
    }
  }
}, {
  path: "/roleprivilege/businessRole/add",
  create: (props) => {
    const { match } = props
    return {
      title: `业务新增`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/BusinessEditView")
    }
  }
}, {
  path: "/roleprivilege/businessRole/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑业务角色`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/BusinessRoleEditView")
    }
  }
}, {
  path: "/roleprivilege/roleManager",
  create: (props) => {
    const { match } = props
    return {
      title: `角色管理`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/RoleManagerListView")
    }
  }
}, {
  path: "/roleprivilege/privilege/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `权限列表${match.params.id}`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/RolePrivilegeListView")
    }
  }
}, {
  path: "/roleprivilege/roleManager/add",
  create: (props) => {
    const { match } = props
    return {
      title: `新增角色`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/RoleManagerEditView")
    }
  }
}, {
  path: "/roleprivilege/roleManager/update/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑角色`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/RoleManagerEditView")
    }
  }
}, {
  path: "/roleprivilege/privilegeList/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `角色新增权限列表`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/RoleAddPrivilegeListView")
    }
  }
}, {
  path: "/roleprivilege/merchantRoleManager",
  create: (props) => {
    const { match } = props
    return {
      title: `商户角色管理`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/MerchantRoleListView")
    }
  }
}, {
  path: "/roleprivilege/merchantRoleManager/add/:id/:merchantId",
  create: (props) => {
    const { match } = props
    return {
      title: `更换角色`,
      authority: [AUTHORITY_MERCHANT_PRIVILEGE],
      render: () => import("./view/data/roleprivilege/MerchantRoleAddView")
    }
  }
},])

// 网站配置
factory.push(...[{
  path: "/website/config",
  create: (props) => {
    return {
      title: "网站配置信息列表",
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigInfoListView")
    }
  }
}, {
  path: "/website/config/add",
  create: (props) => {
    const { match } = props
    return {
      title: `添加网站配置信息`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigInfoEditView")
    }
  }
}, {
  path: "/website/config/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑网站配置信息${match.params.id}`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigInfoEditView")
    }
  }
},

{
  path: "/website/configMp",
  create: (props) => {
    return {
      title: `小程序配置信息`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigMpListView")
    }
  }
},
{
  path: "/website/configMp/add",
  create: (props) => {
    const { match } = props
    return {
      title: `新增小程序配置信息`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigMpEditView")
    }
  }
}, {
  path: "/website/configMp/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑小程序配置信息`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigMpEditView")
    }
  }
},

{
  path: "/website/configMn",
  create: (props) => {
    return {
      title: `商户入网配置`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigMnListView")
    }
  }
},
{
  path: "/website/configMn/add",
  create: (props) => {
    const { match } = props
    return {
      title: `新增商户入网配置`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigMnEditView")
    }
  }
}, {
  path: "/website/configMn/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: `编辑商户入网配置`,
      authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/website/ConfigMnEditView")
    }
  }
}


])

// 操作日志
factory.push(...[{
  path: "/operation/log",
  create: (props) => {
    return {
      title: "操作记录",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/operation/operationLog")
    }
  }
},
{
  path: "/operation/defeat",
  create: (props) => {
    return {
      title:"导出失败记录",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/operation/operationDefeat")
    }
  }
},
{
  path: "/operation/diagnostic",
  create: (props) => {
    return {
      title:"系统日志诊断",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/operation/Diagnostic")
    }
  }
}
])

//专区管理
factory.push(...[{
  path: "/zone/service",
  create: (props) => {
    return {
      title: "服务管理",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/zone/ServiceManagementListView")
    }
  }
},{
  path: "/zone/service/add",
  create: (props) => {
    const { match } = props
    return {
      title: "新增服务",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/zone/ServiceManagementEditView")
    }
  }
},
{
  path: "/zone/service/edit/:id",
  create: (props) => {
    const { match } = props
    return {
      title: "编辑服务",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/zone/ServiceManagementEditView")
    }
  }
},
{
  path: "/zone/layout",
  create: (props) => {
    return {
      title: "布局管理",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/zone/LayoutManagementListView")
    }
  }
},
{
  path: "/thrid/applies",
  create: (props) => {
    return {
      title: "申请管理",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/thrid/applies")
    }
  }
},
{
  path: "/thrid/merchant",
  create: (props) => {
    return {
      title: "商户管理",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/thrid/MerchantListView")
    }
  }
},
{
  path: "/thrid/merchant/detail/:id",
  create: (props) => {
    return {
      title: "商户详情",
      // authority: [MERCHANT_WEBSITE_CONFIG],
      render: () => import("./view/data/thrid/MerchantShowView")
    }
  }
}
])

export const config = {
  menus,
  viewFactory: factory
}