import { message } from "antd"
import _ from "lodash"
import { createBrowserHistory } from "history"

export const globalHistory = createBrowserHistory({
  // basename: "/gd/hrsaas/platform-boss"
  basename: (<any>window).env?.staticPath
})


export function logout() {
  sessionStorage.removeItem("token")
}

export function getToken() {
  return sessionStorage.getItem("token")
}

export function wsPath(path: string) {
  let protocol = "ws:"
  if (document.location.protocol === "https:") {
    protocol = "wss:"
  }
  return `${protocol}//${document.location.host}${path}`
}

export const showErrorMessage = _.throttle(message.error, 5000)

type a = { [key: string]: string }

/**获取location的查询参数 */
export function getLocationSearch(): { [key: string]: string } {
  const search = document.location.search
  if (search.length >= 1) {
    const list = search.substring(1).split("&")
    return list.reduce((a, b) => {
      const kv = b.split("=")
      const copy = {
        ...a,
      }
      copy[kv[0]] = kv[1]
      return copy
    }, {} as any)
  } else {
    return {}
  }
}