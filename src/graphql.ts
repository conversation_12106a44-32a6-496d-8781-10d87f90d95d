import gql from "graphql-tag";
import { ApolloClient, DefaultOptions } from 'apollo-client';
import { InMemoryCache, NormalizedCacheObject } from 'apollo-cache-inmemory';
import { HttpLink } from 'apollo-link-http';
import { ApolloLink } from 'apollo-link'
import { onError } from "apollo-link-error";
import { showErrorMessage, globalHistory, logout } from "./utils";
import { enviroment } from "./env";


// 获取钉签约授权列表
export const queryOutSideMerchants = gql`
  query queryOutSideMerchants($keywords: String, $option: FwQueryOption) {
    outSideMerchant {
      queryOutsideMerchant(keywords: $keywords, option: $option){
        total
        data {
          merchantName
          merchantId
          applyUserName
          applyUserCellPhone
          signingDate
          applyStatus
          totalAmount
          planTotal
          planRemain
        }
      }
    }
  }
`
// 更新审核状态
export const updateOutSideMerchant = gql`
mutation updateOutSideMerchant($id: ID, $authStatus: String, $remark: String) {
    outSideMerchantMutation {
      updateMerchantAuthStatus(id: $id, authStatus:$authStatus, remark: $remark)
    }
}
`
// 获取充值记录列表
export const queryMerchantPricePlan = gql`
query queryMerchantPricePlan($id: ID) {
    outSideMerchant {
      queryMerchantPricePlan(id: $id){
          planName
          rechargeRecordId
          rechargeAmount
          packageNum
          rechargeTime
          operator
      }
    }
  }
`

// 钉签约充值
export const addPricePlan = gql`
mutation addPricePlan($id: ID,$packageName: String, $amount: String, $packageNum: Int,$token: String){
    outSideMerchantMutation {
      addPricePlan(id: $id, packageName: $packageName, amount: $amount, packageNum: $packageNum,token: $token)

    }
}
`


export const queryProperties = gql`
query queryProperties {
  properties {
    bossLoginUrl
  }
}
`

export const bossAuthorize = gql`
mutation bossAuthorize($token: String) {
  bossAuthorize(token: $token)
}
`


export const loginMutation = gql`
mutation login($name: String!, $password: String!) {
  login(name: $name, password: $password)
}
`


export const changePasswordMutation = gql`
mutation changePassword($oldPassword: String!, $newPassword: String!) {
  changePassword(oldPassword: $oldPassword, newPassword: $newPassword) {
    user {
      id
      name
      displayName
      email
      phone
    }
  }
}
`

export const changeProfileMutation = gql`
mutation changeProfile($email: String, $phone: String) {
  changeProfile(email: $email, phone: $phone) {
    user {
      id
      name
      displayName
      email
      phone
    }
  }
}
`

export const profile = gql`
query profile {
  profile {
    id
    name
    authorities
  }
}
`

export const queryTenants = gql`
query queryTenants($keywords: String, $option: FwQueryOption) {
  authz {
    tenants(keywords: $keywords, option: $option) {
      total
      data {
        id
        name
        merchantId
        merchant {
          id
          name
        }
        roles {
          id
          name
        }
      }
    }
  }
}
`

export const queryAuthorizationRecords = gql`
  query queryAuthorizationRecords($resourceOwner: String, $createTime: String,$option: FwQueryOption,$keywords: String) {
    authz {
      authorizationRecords(resourceOwner: $resourceOwner, createTime: $createTime,option: $option,keywords: $keywords) {
        total
        data {
          id
          ownerType
          resourceOwner
          targetType
          target
          restrict
          createTime
          modifyTime
        }
      }
    }
  }
`

export const queryApplicationServerProviders = gql`
  query queryApplicationServerProviders($id: ID, $keywords: String, $option: FwQueryOption) {
    authz {
      applicationServerProviders(id: $id, keywords: $keywords, option: $option) {
        total
        data {
          id
          tenantId
          logoArchiveId
          createTime
          modifyTime
        }
      }
    }
  }
`

export const queryApplicationServerProvider = gql`
  query queryAuthorizationScope($id: ID) {
    authz {
      applicationServerProvider(id: $id){
        id
        tenantId
        logoArchiveId
        logoArchiveUrl
        createTime
        modifyTime
      }
    }
  }
`

export const queryAuthorizationRecord = gql`
query queryAuthorizationRecord($id: ID) {
  authz {
    authorizationRecord(id: $id) {
      id
      ownerType
      resourceOwner
      targetType
      target
      restrict
    }
  }
}
`

export const queryTenant = gql`
  query queryTenant($id: ID) {
    authz {
      tenant(id: $id) {
        id
        name
        merchantId
        roles {
          id
          name
        }
      }
    }
  }
`

export const updateTenant = gql`
mutation updateTenant($id: ID, $name: String, $roles: [ID], $merchant: ID) {
  authz {
    updateTenant(id: $id, name: $name, roles: $roles, merchant: $merchant)
  }
}
`

export const updateAuthorizationRecord = gql`
  mutation updateAuthorizationRecord($id: ID, $ownerType: String, $resourceOwner: String,$targetType: String,$target: String,$restrict: [String]) {
    authz {
      updateAuthorizationRecord(id: $id, ownerType: $ownerType, resourceOwner: $resourceOwner, targetType: $targetType,target: $target, restrict : $restrict)
    }
}
`

export const updateAuthorizationScope = gql`
  mutation updateAuthorizationScope($id: ID, $name: String, $description: String) {
    authz {
      updateAuthorizationScope(id: $id, name: $name, description:$description)
    }
}
`
export const updateTenantCustomer = gql`
  mutation updateTenantCustomer($id: ID, $tenantId: ID, $customerMerchantId: String, $status: String) {
    authz {
      updateTenantCustomer(id: $id, tenantId: $tenantId, customerMerchantId:$customerMerchantId, status:$status)
    }
}
`

export const queryDevelopers = gql`
query queryDevelopers($tenant: ID, $keywords: String, $option: FwQueryOption) {
  authz {
    developers(tenant: $tenant, keywords: $keywords, option: $option) {
      total
      data {
        id
        privateKey
        flags
        callbackUrl
        allowAddress
      }
    }
  }
}
`
export const queryDeveloper = gql`
query queryDeveloper($id: ID) {
  authz {
    developer(id: $id) {
      id
      flags
      callbackUrl
      allowAddress
      privateKey
    }
  }
}
`
export const updateDeveloper = gql`
mutation updateDeveloper($id: ID, $tenant: ID, $flags: [String], $callbackUrl: String, $allowAddress: [String], $privateKey: String) {
  authz {
    updateDeveloper(id: $id, tenant: $tenant, flags: $flags, callbackUrl: $callbackUrl, allowAddress: $allowAddress, privateKey: $privateKey)
  }
}
`


export const queryRoles = gql`
query queryRoles($keywords: String, $option: FwQueryOption) {
  authz {
    roles(keywords: $keywords, option: $option) {
      total
      data {
        id
        name
        access
      }
    }
  }
}
`
export const queryRole = gql`
query queryRole($id: ID) {
  authz {
    role(id: $id) {
      id
      name
      access
    }
  }
}
`
export const updateRole = gql`
mutation updateRole($id: ID, $name: String, $access: [String]) {
  authz {
    updateRole(id: $id, name: $name, access: $access)
  }
}
`

export const userQuery = gql`
query userQuery($keywords: String, $option: FwQueryOption) {
  user(keywords: $keywords, option: $option) {
    total
    data {
      id
      name
      displayName
      email
      disabled
    }
  }
}
`

export const queryMerchant = gql`
query queryMerchant($keywords: String, $option: FwQueryOption) {
  merchant {
    merchants(keywords: $keywords, option: $option) {
      total
      data {
        id
        name
        introduction
        email
        registerAddress
        invoiceAddress
        invoiceMobile
        platformUserNo
        legal
        legalIdCardNo
        unifiedCode
        createTime
      }
    }
  }
}
`

export const promotionProductQuery = gql`
query promotionProductQuery($option: FwQueryOption) {
  promotion {
    promotionProducts(option: $option) {
      data {
        id
        products
        notifyMobile
        updateTime
        status
        channelType
      }
    }
  }
}`


export const potentialCustomerQuery = gql`
query potentialCustomerQuery($keywords: String, $product:String, $createTime: String, $isLinkedUp: String ,$option: FwQueryOption) {
  promotion {
    potentialCustomers(keywords: $keywords, product: $product, createTime: $createTime , isLinkedUp: $isLinkedUp, option: $option) {
      total
      data {
        id
        companyName
        contactName
        contactMobile
        channelType
        potentialProduct
        createTime
        isLinkedUp
      }
    }
  }
}`

export const queryPromotionProduct = gql`
query queryPromotionProduct($id: ID) {
  promotion {
    promotionProduct(id: $id) {
      id
      products
      notifyMobile
      updateTime
      status
      channelType
    }
  }
}`

export const updatePromotionProduct = gql`
mutation updatePromotionProduct($id: ID, $status: String, $notifyMobile: String, $products: String) {
  promotion {
    updatePromotionProduct(id: $id, status: $status, notifyMobile: $notifyMobile, products: $products)
  }
}`


export const queryPotentialCustomer = gql`
query queryPotentialCustomer($id: ID) {
  promotion {
    potentialCustomer(id: $id) {
      id
      companyName
      contactName
      contactMobile
      potentialProduct
      isLinkedUp
      channelType
      remark
      createTime
    }
  }
}
`

export const updatePotentialCustomer = gql`
mutation updatePotentialCustomer($id: ID, $isLinkedUp: Boolean, $remark: String) {
  promotion {
    updatePotentialCustomer(id: $id, isLinkedUp: $isLinkedUp, remark: $remark)
  }
}

`

export const queryProtocols = gql`
query queryProtocols($keywords: String, $businessType: String, $status: String, $merchantId: ID, $tenantId: ID, $signWay: String, $validityStart: String, $validityEnd: String,   $option: FwQueryOption) {
  protocol {
    queryProtocols(keywords: $keywords, businessType: $businessType, status: $status, merchantId: $merchantId,tenantId: $tenantId, signWay: $signWay, validityStart: $validityStart,validityEnd: $validityEnd, option: $option) {
      total
      data{
        id
        protocolName
        businessType
        status
        merchantId
        tenantId
        signWay
        contractId
        signCount
        protocolCreateTime
        validityStart
        validityEnd
        completeTime
        updateTime
        createTime
        merchantId
      }
    }
  }
}
`

export const queryProtocolSigner = gql`
query queryProtocolSigner($protocolId: ID, $option: FwQueryOption) {
  protocol{
    queryProtocolSigners(protocolId: $protocolId, option: $option){
      total
      data{
        id
        identify
        identifyType
        userId
        completeTime
        seqNum
        name 
      }
    }
  }
}
`


export const queryProtocolAttachment = gql`
query queryProtocolAttachment($protocolId: ID, $option: FwQueryOption) {
  protocol{
    queryProtocolAttachment(protocolId: $protocolId, option: $option){
     total
     data{
       id
       protocolId
       archiveId
       createTime
     }
   }
 }
}
`


export const queryMailAccounts = gql`
query queryMailAccounts($keywords: String, $option: FwQueryOption) {
  mail {
    mailAccounts(keywords: $keywords, option: $option) {
      total
      data {
        id
        code
        describe
        protocol
        port
        mailHost
        mailAddress
        mailNickname
        isAuth
        enabledSsl
        createTime
      }
    }
  }
}
`

export const queryMailAccount = gql`
  query queryMailAccount($id: ID) {
    mail {
      mailAccount(id: $id) {
        id
        code
        describe
        protocol
        port
        mailHost
        mailAddress
        mailNickname
        mailPassword
        isAuth
        enabledSsl
        enabledDebugMod
        createTime
      }
    }
  }
`
export const updateMailAccount = gql`
  mutation updateMailAccount($id: ID, $code: String, $describe: String, $protocol: String, $port: String, $mailHost: String, $mailAddress: String, $mailNickname: String, $mailPassword: String, $isAuth: Boolean, $enabledDebugMod: Boolean, $enabledSsl: Boolean) {
    mail {
      updateMailAccount(id: $id, code: $code, describe: $describe, protocol: $protocol, port: $port, mailHost: $mailHost, mailAddress: $mailAddress, mailNickname: $mailNickname, mailPassword: $mailPassword, isAuth: $isAuth, enabledDebugMod: $enabledDebugMod, enabledSsl: $enabledSsl)
    }
  }
`

export const checkSendMail = gql`
  mutation checkSendMail($mailCode: String, $to: String) {
    mail {
      checkSendMail(mailCode: $mailCode, to: $to)
    }
  }
`

export const queryMailTemplates = gql`
  query queryMailTemplates($keywords: String, $option: FwQueryOption) {
    mail {
      mailTemplates(keywords: $keywords, option: $option) {
        total
        data {
          id
          code
          name
          content
          createTime
        }
      }
    }
  }
`
export const queryMailTemplate = gql`
  query queryMailTemplate($id: ID) {
    mail {
      mailTemplate(id: $id) {
        id
        code
        name
        content
        createTime
      }
    }
  }
`
export const updateMailTemplate = gql`
  mutation updateMailTemplate($id: ID, $code: String, $name: String, $content: String) {
    mail {
      updateMailTemplate(id: $id, code: $code, name: $name, content: $content)
    }
  }
`
export const querySendMailRecords = gql`
  query querysendMailRecords($to: String, $startTime: DateTime, $endTime: DateTime, $option: FwQueryOption) {
    mail {
      sendMailRecords(to: $to, startTime: $startTime, endTime: $endTime, option: $option) {
        total
        data {
          id
          mailCode
          templateCode
          to
          parameter
          result
          errorInfo
          createTime
        }
      }
    }
  }
`


export const querySmsConfigs = gql`
query querySmsConfigs($systemCode: String, $option: FwQueryOption) {
  sms{
    smsConfigs(systemCode: $systemCode, option: $option){
     total
     data{
      id
      smsCode
      systemName
      systemCode
      privateKey
      createTime
      updateTime
     }
   }
 }
}
`
export const querySmsConfig = gql`
  query querySmsConfig($id: ID) {
    sms{
      smsConfig(id: $id){
        id
        smsCode
        systemName
        systemCode
        privateKey
        createTime
        updateTime
      }
    }
  }
`

export const querySmsLanmaolyConfigs = gql`
  query querySmsLanmaolyConfigs($name: String,$systemName: String,$bizName: String, $option: FwQueryOption) {
    sms{
      smsLanmaolyConfigs(name: $name,systemName: $systemName,bizName: $bizName, option: $option){
        total
        data{
          id
          name
          systemName
          bizName
          privateKey
          sign
        }
      }
    }
  }
`
export const querySmsLanmaolyConfig = gql`
  query querySmsLanmaolyConfig($id: ID) {
    sms{
      smsLanmaolyConfig(id: $id){
        id
        name
        systemName
        bizName
        privateKey
        sign
      }
    }
  }
`




export const updateSmsConfig = gql`
mutation updateSmsConfig($id: ID, $smsCode: String,  $systemCode: String, $systemName: String, $privateKey: String) {
  smsConfig {
    updateSmsConfig(id: $id, smsCode: $smsCode, systemCode: $systemCode, systemName: $systemName, privateKey: $privateKey)
  }
}
`

export const updateSmsChannel = gql`
  mutation updateSmsChannel($id: ID, $name: String,  $provider: String, $privateKey: String) {
    smsConfig {
      updateSmsChannel(id: $id, name: $name, provider: $provider, privateKey: $privateKey)
    }
  }
`

export const updateSmsbusiness = gql`
  mutation updateSmsbusiness($id: String, $description: String, $channelId: ID, $sign: String, $templateId: ID) {
    smsConfig {
      updateSmsBusiness(id: $id, description: $description, channelId: $channelId, sign: $sign, templateId: $templateId)
    }
  }
`

export const updateSmsTemplate = gql`
  mutation updateSmsTemplate($id: ID, $name: String, $template: String, $description: String) {
    smsConfig {
      updateSmsTemplate(id: $id, name: $name, template: $template, description: $description)
    }
  }
`

export const updateSmsLanmaolyConfig = gql`
  mutation updateSmsLanmaolyConfig($id: ID, $name: String, $systemName: String, $privateKey: String, $bizName: String, $sign: String) {
    smsConfig {
      updateSmsLanmaoConfig(id: $id, name: $name, systemName: $systemName, privateKey: $privateKey, bizName: $bizName, sign: $sign)
    }
  }
`
export const smsSendRecords = gql`
query smsSendRecords($mobile: String, $systemCode: String, $businessCode: String, $startTime: String, $endTime: String, $option: FwQueryOption) {
  sms{
    smsSendRecords(mobile: $mobile, systemCode: $systemCode, businessCode: $businessCode, startTime: $startTime,endTime: $endTime, option: $option){
     total
     data{
      id
      mobile
      remark
      createTime
      updateTime
      status
      requestParams
      systemCode
      businessCode
     }
   }
 }
}
`




export const querySmsTemplates = gql`
query querySmsTemplates($keywords: String, $option: FwQueryOption) {
  sms {
    smsTemplates(keywords: $keywords, option: $option) {
      total
      data {
        id
        name
        description
        template
      }
    }
  }
}
`
export const querySmsTemplate = gql`
  query querySmsTemplate($id: ID) {
    sms{
      smsTemplate(id: $id){
        id
        name
        template
        description
      }
    }
  }
`

export const querySmsBusinesses = gql`
query querySmsBusinesses($keywords: String, $option: FwQueryOption) {
  sms {
    smsBusinesses(keywords: $keywords, option: $option) {
      total
      data {
        id
        description
        sign
        template {
          id
          name
        }
        channel{
          id
          name
        }
      }
    }
  }
}
`

export const querySmsBusiness = gql`
    query querySmsBusiness($id: String) {
      sms{
        smsBusiness(id: $id){
          id
          template {
             id
             name
          }
          channel{
             id
             name
          }
          description
          sign
       }
    }
}
`

export const querySmsChannels = gql`
  query querySmsChannels($keywords: String, $option: FwQueryOption) {
    sms {
      smsChannels(keywords: $keywords, option: $option) {
        total
        data {
          id
          name
          provider
          privateKey
        }
      }
    }
  }
`
export const querySmsChannel = gql`
  query querySmsChannel($id: ID) {
    sms{
      smsChannel(id: $id){
        id
        name
        provider
        privateKey
      }
    }
  }
`


export const queryAgentInfos = gql`
query queryAgentInfos($keywords: String, $option: FwQueryOption) {
  agentInfo {
    agentInfos(keywords: $keywords, option: $option){
     total
     data{
      id
      name
      introduction
      archiveId
      show
      createTime
     }
   }
 }
}
`

export const queryAgentInfo = gql`
query queryAgentInfo($id: ID) {
  agentInfo {
    agentInfo(id: $id){
      id
      name
      introduction
      archiveId
      archiveUrl
      show
      createTime
   }
 }
}
`

export const updateAgentInfo = gql`
mutation updateAgentInfo($id: ID, $name: String,  $introduction: String, $archiveId: String, $show: Boolean) {
  agentInfo {
    updateAgentInfo(id: $id, name: $name, introduction: $introduction, archiveId: $archiveId, show: $show)
  }
}
`

export const updateApplicationServerProvider = gql`
  mutation updateApplicationServerProvider($id: ID, $tenantId: ID,  $logoArchiveId: String) {
    authz {
      updateApplicationServerProvider(id: $id, tenantId: $tenantId, logoArchiveId: $logoArchiveId)
    }
  }
`

export const queryApplications = gql`
query queryApplications($keywords: String, $code: String, $option: FwQueryOption) {
  applicationGroup {
    applications(keywords: $keywords, code: $code, option: $option){
     total
     data{
      id
      name
      code
     }
   }
 }
}
`

export const queryApplicationGroups = gql`
query queryApplicationGroups($keywords: String, $option: FwQueryOption) {
  applicationGroup {
    applicationGroups(keywords: $keywords, option: $option){
     total
     data{
      id
      name
      archiveId
      applications {
        name,
        code
      }
      enable
      createTime
     }
   }
 }
}
`

export const queryApplicationGroup = gql`
query queryApplicationGroup($id: ID) {
  applicationGroup {
    applicationGroup(id: $id){
      id
      name
      archiveId
      archiveUrl
      applications {
        name,
        code
      }
      enable
      createTime
   }
 }
}
`

export const queryRolePrivilege = gql`
  query queryRolePrivilege($keywords: String, $option: FwQueryOption) {
    merchantPrivileges {
      queryRole(keywords: $keywords, option: $option){
        total,
        data{
          id,
          roleName,
          roleCode,
          describe,
          enabled,
          seq,
          lastUpdateTime
        }
      }
    }
  }
`

export const queryPrivilegeList = gql`
    query queryPrivilegeList($keywords: String, $option: FwQueryOption, $id: ID) {
      merchantPrivileges {
            queryPrivileges(keywords: $keywords, option: $option, id: $id){
                total,
                data{
                    id,
                    name,
                    code
                }
            }
        }
    }
`

export const queryPrivilegeByRoleId = gql`
  query queryPrivilegeByRoleId($roleId: ID) {
    merchantPrivileges {
      queryPrivilegeByRoleId(roleId: $roleId){
        id,
        name
      }
    }
  }
`


export const queryPrivilegeGroup = gql`
  query queryPrivilegeGroup($roleId: ID) {
    merchantPrivileges {
      getPrivilegeGroup(roleId: $roleId){
        id,
        name,
        parentId,
        businessCode
        children{
          id,
          name,
          businessCode
          children{
            id,
            name,
            businessCode
            children{
              id,
              name,
              businessCode
              children{
                id,
                name,
                businessCode
                children{
                  id,
                  name,
                  businessCode
                }
                privileges{
                  id,
                  name,
                  code
                }
              }
              privileges{
                id,
                name,
                code
              }
            }
            privileges{
              id
              name,
              code
            }
          }
          privileges{
            id,
            name,
            code
          }
        }
        privileges{
          id,
          name,
          code
        }
      }
    }
  }
`


export const queryBusinesses = gql`
  query queryBusinesses($keywords: String, $option: FwQueryOption) {
    merchantPrivileges {
      findBusinesses(keywords: $keywords, option: $option){
         total,
         data{
           id,
           name,
           remark,
           createTime,
        }
     }
   }
  }
`

export const getMerchant = gql`
  query getMerchant($id: ID) {
    applicationGroup {
      getMerchant(id: $id){
        id,
        name
      }
    }
  }
`

export const getMerchantUser = gql`
  query getMerchantUser($id: ID) {
    merchantPrivileges {
      queryMerchant(id: $id){
        id,
        name,
        roles {
          id,
          roleName
          }
        }
      }
    }
`

export const getMerchantRoles = gql`
  query getMerchantRoles($id: ID) {
    merchantPrivileges {
      getMerchantRoles(merchantId: $id){
        id
        roleName
      }
    }
  }
`

export const queryMerchants = gql`
  query queryMerchants($keywords: String, $option: FwQueryOption) {
    merchantPrivileges {
      getMerchants(keywords: $keywords, option: $option){
        total,
        data{
          id,
          name,
          email,
          createTime
        }
      }
    }
  }
`

export const queryBusiness = gql`
  query queryBusiness($id: String) {
    merchantPrivileges {
      findBusiness(id: $id){
        id,
        name,
        remark,
        createTime,
        roles {
            id,
            roleName
        }
      }
    }
  }
`

export const querySystemRole = gql`
  query querySystemRole($keywords: String, $option: FwQueryOption) {
    applicationGroup {
      queryRole(keywords: $keywords, option: $option){
        total,
        data{
          id,
          roleName,
          roleCode,
          describe,
          enabled,
          seq,
          lastUpdateTime
        }
      }
    }
  }
`

export const querySystemEnableRole = gql`
  query querySystemRole {
    merchantPrivileges {
      querySystemEnableRole{
          id,
          roleName,
          roleCode,
          describe,
          enabled,
          lastUpdateTime
      }
    }
  }
`

export const getRole = gql`
  query querySystemRole($id: ID) {
    merchantPrivileges {
      querySystemRole(id: $id){
        id,
        roleName,
        describe,
        roleCode,
        seq
      }
    }
  }
`

export const updateApplicationGroup = gql`
mutation updateApplicationGroup($id: ID, $name: String, $archiveId: String, $enable: Boolean, $applications: [String]) {
  applicationGroup {
    updateApplicationGroup(id: $id, name: $name, archiveId: $archiveId, enable: $enable, applicationCodes: $applications)
  }
}
`

export const businessBindRole = gql`
  mutation updateBusinessBindRole( $businessId: String, $roleIds: [ID]) {
    merchantPrivileges {
      businessBindRole( businessId: $businessId, roleIds: $roleIds)
    }
  }
`


export const merchantUserBindRole = gql`
  mutation updateMerchantUserBindRole( $merchantId: String, $roleIds: [ID]) {
    merchantPrivileges {
      changeBindRoles( merchantId: $merchantId, roleIds: $roleIds)
    }
  }
`
export const updateRoleBindPrivileges = gql`
    mutation updateRoleBindPrivileges( $roleId: String, $privilegeIds: [ID]) {
      merchantPrivileges {
            roleBindPrivileges( roleId: $roleId, privilegeIds: $privilegeIds)
        }
    }
`


export const updateBusiness = gql`
  mutation updateBusiness( $name: String, $remark: String) {
    merchantPrivileges {
      addBusiness( name: $name, remark: $remark)
    }
  }
`


export const updateEnable = gql`
  mutation updateEnable( $roleId: ID, $enable: Boolean) {
    merchantPrivileges {
      updateEnable( roleId: $roleId, enable: $enable)
    }
  }
`
export const updateRolePrivilege = gql`
    mutation updateRolePrivilege( $roleId: ID,  $roleName: String, $roleCode:String, $describe:String, $seq:String) {
      merchantPrivileges {
            updateRolePrivilege( roleId: $roleId, roleName: $roleName,roleCode:$roleCode, describe:$describe, seq:$seq)
        }
    }
`

export const deletePrivilege = gql`
    mutation deletePrivilege($id: ID) {
        merchantPrivileges {
            deletePrivilege(id: $id)
        }
    }
`

export const deletePrivilegeGroup = gql`
    mutation deletePrivilegeGroup($id: ID) {
        merchantPrivileges {
            deletePrivilegeGroup(id: $id)
        }
    }
`

export const queryAuthorizationScopes = gql`
query queryAuthorizationScopes($id: ID, $keywords: String, $option: FwQueryOption) {
  authz {
    authorizationScopes(id: $id, keywords: $keywords, option: $option){
      total
      data{
        id
        name
        description
        createTime
        modifyTime
      }
   }
 }
}
`

export const queryAuthorizationScope = gql`
query queryAuthorizationScope($id: ID) {
  authz {
    authorizationScope(id: $id){
      id
      name
      description
      createTime
      modifyTime
   }
 }
}
`
export const queryTenantCustomers = gql`
query queryTenantCustomers($keywords: String, $option: FwQueryOption) {
  authz {
    tenantCustomers(keywords: $keywords, option: $option){
      total
      data{
        id
        tenantId
        status
        createTime
        modifyTime
        customerMerchantId
      }
   }
 }
}
`

export const queryTenantCustomer = gql`
query queryTenantCustomer($id: ID) {
  authz {
    tenantCustomer(id: $id){
      id
      tenantId
      status
      createTime
      modifyTime
      customerMerchantId
   }
 }
}
`

export const queryArchives = gql`
query queryArchives($keywords: String, $uploadTime: String, $expired: String, $option: FwQueryOption) {
  archiveInfo {
    archiveInfos(keywords: $keywords, uploadTime: $uploadTime, expired: $expired, option: $option){
     total
     data{
      id
      name
      remark
      size
      uploadTime
      expired
      uploadStatus
     }
   }
 }
}
`
export const deleteArchive = gql`
mutation deleteArchive($id: String) {
  archiveInfo {
    deleteArchive(id: $id)
  }
}
`
export const deleteArchiveBatches = gql`
mutation deleteArchiveBatches($expired: String) {
  archiveInfo {
    deleteArchiveBatches(expired: $expired)
  }
}
`

export const queryWebsiteConfigInfos = gql`
query queryWebsiteConfigInfos($keywords: String, $option: FwQueryOption) {
  configInfo {
    websiteConfigInfos(keywords: $keywords, option: $option){
     total
     data{
      id
      merchantName
      domainName
      createTime
     }
   }
 }
}
`
export const queryWebsiteConfigInfo = gql`
query queryWebsiteConfigInfo($id: ID) {
  configInfo {
    websiteConfigInfo(id: $id){
      id
      merchantName
      slogan
      domainName
      entranceEnable
      bottomDescription
      ICP
      networkSecurity
      logo
      tabLogo
      agreementName
      agreementContent
      appletQrCode
      createTime
   }
 }
}
`
export const updateWebsiteConfigInfo = gql`
mutation updateWebsiteConfigInfo($id: ID, $merchantName: String, $slogan: String, $domainName: String, $entranceEnable: Boolean, $bottomDescription: String, $ICP: String, $networkSecurity: String, $logo: String, $tabLogo: String, $agreementName: String, $agreementContent: String, $appletQrCode: String) {
  configInfo {
    updateWebsiteConfigInfo(id: $id, merchantName: $merchantName, slogan: $slogan, domainName: $domainName, entranceEnable: $entranceEnable, bottomDescription: $bottomDescription, ICP: $ICP, networkSecurity: $networkSecurity, logo: $logo, tabLogo: $tabLogo, agreementName: $agreementName, agreementContent: $agreementContent, appletQrCode: $appletQrCode)
  }
}
`
export const queryAppletsConfigInfos = gql`
query queryAppletsConfigInfos($keywords: String, $option: FwQueryOption) {
  configInfo {
    appletsConfigInfos(keywords: $keywords, option: $option){
     total
     data{
      logo
      name
      prompt
      agreementName
      agreementContent
      remark
      createTime
      lastUpdateTime
     }
   }
 }
}
`
export const queryAppletsConfigInfo = gql`
query queryAppletsConfigInfo($keywords: String, $option: FwQueryOption) {
  configInfo {
    appletsConfigInfo(keywords: $keywords, option: $option){
      logo
      name
      prompt
      agreementName
      agreementContent
      remark
      createTime
      lastUpdateTime
   }
 }
}
`
export const updateAppletsConfigInfo = gql`
mutation updateAppletsConfigInfo($logo: String, $name: String, $prompt: String, $agreementName: String, $agreementContent: String, $remark: String) {
  configInfo {
    updateAppletsConfigInfo(logo: $logo, name: $name, prompt: $prompt, agreementName: $agreementName, agreementContent: $agreementContent, remark: $remark)
  }
}
`

export const queryMerchantInlineConfigList = gql`
query merchantInlineConfigList($keywords: String, $option: FwQueryOption) {
  configInfo {
    merchantInlineConfigList(keywords: $keywords, option: $option){
     total
     data{
        id
        fieldName
        mustFlag
        showFlag
        remark
     }
   }
 }
}
`

export const queryMerchantInlineConfig = gql`
query merchantInlineConfigDetail($id: ID) {
  configInfo {
    merchantInlineConfigDetail(id: $id){
        id
        fieldName
        mustFlag
        showFlag
        remark
   }
 }
}
`

export const updateMerchantInlineConfig = gql`
mutation modifyMerchantInlineConfig($id: String,$fieldName: String, $mustFlag: String, $showFlag: String, $remark: String) {
  configInfo {
    modifyMerchantInlineConfig(id:$id,fieldName: $fieldName, mustFlag: $mustFlag, showFlag: $showFlag, remark: $remark)
  }
}
`

export const queryThridApplies = gql`
query pageApplyInfo(
  $applyTime: String, 
  $productName: String, 
  $supplierName: String, 
  $merchantName: String, 
  $merchantId: ID, 
  $proposerName: String, 
  $option: FwQueryOption) {
  appstore {
    pageApplyInfo(
      applyTime:$applyTime,
      productName:$productName,
      supplierName:$supplierName,
      merchantName:$merchantName,
      merchantId:$merchantId,
      proposerName:$proposerName,
      option: $option
      ) {
      total
      data {
        applyId
        applyTime
        merchantName
        merchantId
        productName
        supplierName
        proposerId
        proposerName
        proposerEmail
        proposerMobile
        mainBusinessLocation
        status
      }
    }
  }
}
`

export const queryAllThridApplies = gql`
query listApplyInfo(
  $applyTime: String, 
  $productName: String, 
  $supplierName: String, 
  $merchantName: String, 
  $merchantId: ID, 
  $proposerName: String) {
  appstore {
    listApplyInfo(
      applyTime:$applyTime,
      productName:$productName,
      supplierName:$supplierName,
      merchantName:$merchantName,
      merchantId:$merchantId,
      proposerName:$proposerName
      ) {
      total
      data {
        applyId
        applyTime
        merchantName
        merchantId
        productName
        supplierName
        proposerId
        proposerName
        proposerEmail
        proposerMobile
        mainBusinessLocation
      }
    }
  }
}
`

export const queryServiceManagemenetList = gql`
  query pageProductInfo(
    $option: FwQueryOption 
    $name: String, 
    $sn: String) {
    appstore {
      pageProductInfo(
        name: $name, 
        sn: $sn, 
        option: $option 
      ) {
        total
        data {
          id
          name
          sn
          description
          tel
          logoPc
          logoH5
          picturePc
          pictureH5
          type
          status
          supplierName
          useType
          openType
          remark
        }
      }
    }
  }
`;

export const queryServiceManagemenetListById = gql`
    query productInfo($id: ID) {
      appstore {
              productInfo(id: $id){
                  id
                  name
                  sn
                  description
                  tel
                  logoPc
                  logoH5
                  picturePc
                  pictureH5
                  type
                  status
                  supplierName
                  useType
                  openType
                  remark
            }
      }
    }
`
export const updateServiceManagemenetListById = gql`
    mutation updateProduct($id: ID, $name: String, $sn: String, $description: String, $tel: String, $logoPc: String, $logoH5: String, $picturePc: [String],$pictureH5: [String], $type: String, $status: String, $supplierName: String, $useType: String, $openType: String, $remark: String) {
      appstore {
        updateProduct(id: $id, name: $name, sn: $sn, description: $description, tel: $tel, logoPc: $logoPc, logoH5: $logoH5, picturePc: $picturePc,pictureH5:$pictureH5 , type: $type, status: $status, supplierName: $supplierName, useType: $useType, openType: $openType, remark: $remark )
      }
    }
`;
export const addServiceManagemenetList = gql`
    mutation addProduct($name: String, $sn: String, $description: String, $tel: String, $logoPc: String, $logoH5: String, $picturePc: [String],$pictureH5: [String], $type: String, $status: String, $supplierName: String, $useType: String, $openType: String, $remark: String) {
      appstore {
        addProduct(name: $name, sn: $sn, description: $description, tel: $tel, logoPc: $logoPc, logoH5: $logoH5, picturePc: $picturePc,pictureH5:$pictureH5 , type: $type, status: $status, supplierName: $supplierName, useType: $useType, openType: $openType, remark: $remark )
      }
    }
`;

/**取消申请 */
export const cancelApplyById = gql`
    mutation rejectedApply($id: ID) {
      appstore {
        rejectedApply(id: $id )
      }
    }
`;
/**开通服务 */
export const openApplyById = gql`
    mutation agreeApply($id: ID) {
      appstore {
        agreeApply(id: $id )
      }
    }
`;

export const queryMerchantInfoList = gql`
  query pageMerchantInfo(
    $option: FwQueryOption 
    $id: ID, 
    $merchantName: String) {
    appstore {
      pageMerchantInfo(
        id: $id, 
        merchantName: $merchantName, 
        option: $option 
      ) {
        total
        data {
          id
          merchantName
          openedProductName
          applyingProductName
        }
      }
    }
  }
`;
export const queryMerchantInfoListById = gql`
  query merchantInfo(
    $id: ID) {
    appstore {
      merchantInfo(
        id: $id, 
      ) {
          id
          merchantName
          openedProductName
          applyingProductName
      }
    }
  }
`;

export const queryLayoutInfoList = gql`
  query layoutInfo{
    appstore {
      layoutInfo{
        groups {
          groupName
          seq
          members{
            product{
              id
              name
              sn
              description
              tel
              logoPc
              logoH5
              picturePc
              pictureH5
              type
              status
              supplierName
              useType
              openType
              remark
            }
            productId
            seq
            visibility
            tag
          }
        }
  }
}
}
`
export const updateLayoutInfoList = gql`
mutation($groups :[AppstoreUpdateLayoutGroup]){
	appstore{
    saveLayout(groups:$groups)
  }
}
`;

/**
  query layoutInfo($operateTime:String){
    appstore {
      layoutInfo(operateTime: $operateTime){
        groups {
          groupName
          seq
          members {
            productId
            seq
            visibility
            tag
            product {
              id
              name
              sn
              description
              tel
              logoPc
              logoH5
              picturePc
              pictureH5
              type
              status
              supplierName
              useType
              openType
              remark
            }
          }
        }
    }
  }
}
 */

console.log(enviroment)
const cache = new InMemoryCache({});
const httpLink = new HttpLink({
  uri: enviroment?.domain + '/graphql',
});
const middlewareLink = new ApolloLink((operation, forward) => {
  const token = sessionStorage.getItem('token')
  operation.setContext({
    headers: {
      'Authorization': token ? `Bearer ${token}` : null
    }
  })

  return forward(operation)
})
const errorLink = onError(({ response, operation, graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      console.log(`[GraphQL error]: Message: ${message}, Path: ${path}, Code: ${extensions?.code}`)
      showErrorMessage(message)
      if (extensions) {
        if (extensions.code === "2") {
          logout()
          globalHistory.push("/login")
        }
      }
    });
  } else if (networkError) {
    console.log(`[Network error]: ${networkError}`);
    showErrorMessage(networkError.message)
  }
});
const link = middlewareLink.concat(errorLink).concat(httpLink)

const defaultOptions: DefaultOptions = {
  watchQuery: {
    fetchPolicy: 'no-cache',
  },
  query: {
    fetchPolicy: 'no-cache',
  },
}

export const apolloClient: ApolloClient<NormalizedCacheObject> = new ApolloClient({
  cache,
  link,
  defaultOptions
});
