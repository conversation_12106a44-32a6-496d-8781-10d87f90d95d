
import React, {  } from 'react';
import { UserProfile, useCatchedMutation } from "@lanmaoly/admin-framework";
import { useQuery } from '@apollo/react-hooks';
import { Spin } from 'antd';
import { profile, changePasswordMutation, changeProfileMutation } from '../graphql';


export default () => {

  const { loading, data, error } = useQuery(profile)
  const [changePassword] = useCatchedMutation(changePasswordMutation)
  const [changeProfile] = useCatchedMutation(changeProfileMutation)

  if (loading) {
    return <Spin spinning={true} />
  }
  const user = data.profile.user
  if (error || !user) {
    return <div>出错了</div>
  }

  return <div>
    <UserProfile user={user.name} displayName={user.displayName} phone={user.phone} email={user.email}
      onChangePassword={async (v) => {
        changePassword({
          variables: {
            oldPassword: v.old,
            newPassword: v.new
          }
        }).catch(r => {
          console.log(r)
        })
      }}
      onChangeUserInfo={async (values) => {
        changeProfile({
          variables: {
            email: values.email,
            phone: values.phone
          }
        })
      }} />
  </div>
}