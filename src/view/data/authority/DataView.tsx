
import { Input, Button, Form, Upload, Switch, message } from 'antd';
import React, { useContext, useState } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { apolloClient, deleteArchive, queryArchives } from '../../../graphql';

import { DatePicker } from 'antd';
import { enviroment } from '../../../env';


const UploadField = (props: {
  value?: any
  onChange?: (value: any) => void
}) => {
  const { value } = props
  return <Upload name="file" fileList={value ? [value] : void (0)} onChange={(info) => {
    if (props.onChange) {
      props.onChange(info.file)
    }
  }}
    itemRender={() => null} beforeUpload={() => {
      return false
    }}>
    <Input readOnly={true} value={value?.name} />
  </Upload>
}


const importPrivilegesUrl = enviroment.domain + "/api/authority/importPrivileges";
const exportPrivilegesUrl = enviroment.domain + "/api/authority/exportPrivileges";


const importPrivilegeGroupUrl = enviroment.domain + "/api/authority/importPrivilegeGroup";
const exportPrivilegeGroupUrl = enviroment.domain + "/api/authority/exportPrivilegeGroup";


export default () => {

  const [instance] = useContext(ApplicationContext)
  const [form] = Form.useForm()
  const [form2] = Form.useForm()
  const [loading, setLoading] = useState(false)

  return <div>
    <Form form={form} layout="inline" initialValues={{
      replaceAll: false
    }}>
      <Form.Item label="权限数据文件" name="file">
        <UploadField />
      </Form.Item>
      <Form.Item name="replaceAll" label="是否全量替换">
        <Switch></Switch>
      </Form.Item>
      <Button.Group>
        <Button loading={loading} type="primary" onClick={() => {
          const v = form.getFieldsValue()
          const formData = new FormData();
          for (let key in v) {
            formData.append(key, v[key])
          }
          setLoading(true)
          fetch(importPrivilegesUrl, {
            method: 'POST',
            body: formData
          }).then(r => r.json()).then(o => {
            if (!o.success) {
              message.error(o.message)
            }
          }).finally(() => {
            setLoading(false)
          })
        }}>导入权限</Button>
        <Button onClick={() => {
          fetch(exportPrivilegesUrl).then(r => r.blob()).then(blob => {
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = "权限.json";
            document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
            a.click();
            a.remove();  //afterwards we remove the element again     
          })
        }}>导出权限</Button>
      </Button.Group>
    </Form>

    <Form form={form2} layout="inline" initialValues={{
      replaceAll: false
    }}>
      <Form.Item label="权限组数据文件" name="file">
        <UploadField />
      </Form.Item>
      <Button.Group>
        <Button loading={loading} type="primary" onClick={() => {
          const v = form2.getFieldsValue()
          const formData = new FormData();
          for (let key in v) {
            formData.append(key, v[key])
          }
          setLoading(true)
          fetch(importPrivilegeGroupUrl, {
            method: 'POST',
            body: formData
          }).then(r => r.json()).then(o => {
            if (!o.success) {
              message.error(o.message)
            }
          }).finally(() => {
            setLoading(false)
          })
        }}>导入权限组</Button>
        <Button onClick={() => {
          fetch(exportPrivilegeGroupUrl).then(r => r.blob()).then(blob => {
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = "权限组.json";
            document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
            a.click();
            a.remove();  //afterwards we remove the element again     
          })
        }}>导出权限组</Button>
      </Button.Group>
    </Form>
  </div>
}