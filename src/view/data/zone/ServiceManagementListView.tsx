import React,{useContext}   from 'react';
import { Input, Button} from 'antd';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, useQueryStage, DataTableOperation, FaIcon, ApplicationContext } from '@lanmaoly/admin-framework';
import { queryServiceManagemenetList } from '../../../graphql';
const SerivceNameFilter = withForm(Input, {
  label: '服务名称',
  name: 'name',
})
const ServiceCodeFilter = withForm(Input, {
  label: '服务编码',
  name: 'sn',
})
export default () => {
    const filters: QueryStageFilterOption[] = [{
      node: () => <SerivceNameFilter/>
    },{
      node: () => <ServiceCodeFilter/>
    }]
    const [instance] = useContext(ApplicationContext)
    const columns: DataTableColumnProps = [{
      title: '服务编码',
      dataIndex: 'sn',
      width: 200
    }, {
      title: '服务名称',
      dataIndex: 'name',
      width: 200,
    }, {
      title: '一句话描述',
      dataIndex: 'description',
      width: 200,
    }, {
      title: '服务归属类型',
      dataIndex: 'type',
      width: 200,
      render(val) {
        const obj = {
          OWN_BRAND: "自有",
          THIRD_PARTY: "第三方",
        };
        return obj[val];
      }
    }, {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 200,
    }, {
      title: '服务接入方式',
      dataIndex: 'useType',
      width: 200,
      render(val) {
        const obj = {
          CONVERGED_SOLUTIONS: "融合解决方案",
          PLATFORM_ACCESS: "平台接入",
          SERVICE_DISPLAY: "服务展示",
        };
        return obj[val];
      }
    }, {
      title: '服务开通方式',
      dataIndex: 'openType',
      width: 200,
      render(val) {
        const obj = {
          DEFAULT: "默认开通",
          CLICK_TRY: "点击试用即开通",
          OFFLINE_AUDIT: "线下审核开通",
        };
        return obj[val];
      }
    }]
    const stage = useQueryStage({
      query: queryServiceManagemenetList,
      columns,
      rowKey: "id",
      filters,
      dataPath: (data) => data.appstore.pageProductInfo,
      autoRefresh:true
    })
    const opers: DataTableOperation[] = [{
      title: '编辑',
      icon: <FaIcon name="pencil-square-o" />,
      action: (record) => {
        instance.history.push(`/zone/service/edit/${record.id}`)
      }
    }];
    const buttons = <Button.Group>
            <Button type="primary" onClick={() => {
        instance.history.push('/zone/service/add')
      }}>新增</Button>
    </Button.Group>
    return <div>
       <QueryStage
        instance={stage}
        operation={opers}
        toolbar={buttons}
    />
    </div>
  }