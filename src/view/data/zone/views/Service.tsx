import React, { useEffect, useState } from "react";
import { Image, Card, Modal, Radio, Space } from 'antd';
import ServiceRightMenu from "../services/ServiceRightMenu";


const serviceRightMenuOptions = [
    { id: 0, content: "修改可见性" },
    { id: 1, content: "设置标签" },
    { id: 2, content: "删除", style: { color: 'red' } },
];

const formatServiceRightMenuPosition = (position) => ({
    x: position?.x ?? 0,
    y: position?.y ?? 0,
});

const Service: any = ({title, icon, status, onClick, recommend}) => {
    const [point, setPoint] = useState({ x: 80, y: 80 });
    const [rightMenu, setRightMenu] = useState(false);
    const [isDeleteService, setIsDeleteService] = useState(false);
    const [isServiceUpdate, setIsServiceUpdate] = useState(false);
    const [isSettingTag, setIsSettingTag] = useState(false);

    const [value, setValue] = useState(1);

    const onChange = (e) => {
      setValue(e.target.value);
    };
  
    return <div style={{ position: 'relative' }} >
        <Card id="service" 
        style={{ width: 200, height: 150 , cursor: 'pointer'}}
         bordered={false}
            onClick={() => {
                setRightMenu(true)    
            }}
         >
            <Image
                preview={false}
                width={50}
                src={icon}
            />
            {
                recommend?
                    <span style={{ color: 'red' }}>{recommend}</span>
                        :
                    <></>
            }
            <p>{title}</p>
            <p style={{
                color: 'rgb(185,116,25)'
            }}>{status}</p>
        </Card>
        {
            rightMenu ?      
                <ServiceRightMenu position={formatServiceRightMenuPosition(point)} options={serviceRightMenuOptions} onChose={(item) => {
                    setRightMenu(false);
                    console.log('itme', item);
                    // onClick(item);
                    switch (item.id) {
                        case 0:
                            setIsServiceUpdate(true);
                            break;
                        case 1:
                            setIsSettingTag(true);
                            break;
                        case 2:
                            setIsDeleteService(true);
                            break;
                    }
                }} />
                : 
            <></>
        }
        <Modal
                visible={isDeleteService}
                title="删除分组"
                onOk={() => {
                    setIsDeleteService(false);
                }}
                onCancel={() => {
                    setIsDeleteService(false);
                }}>
            <h3>您确认要删除【】服务吗？</h3>
            <p>删除后，在三方专区中将不会显示此服务，之前已开通的服务可在已开通中查看。</p>
        </Modal>
        <Modal
                visible={isServiceUpdate}
                title="修改服务可见性"
                onOk={() => {
                    setIsServiceUpdate(false);
                }}
                onCancel={() => {
                    setIsServiceUpdate(false);
                }}>
                    <Radio.Group onChange={onChange} value={value}>
                        <Space direction="vertical">
                            <Radio value={1}>1</Radio>
                            <Radio value={2}>2</Radio>
                            <Radio value={3}>3</Radio>
                        </Space>
                </Radio.Group>
        </Modal>
        <Modal
                visible={isSettingTag}
                title="设置标签为"
                onOk={() => {
                    setIsSettingTag(false);
                }}
                onCancel={() => {
                    setIsSettingTag(false);
                }}>
                    <Radio.Group onChange={onChange} value={value}>
                        <Space direction="vertical">
                            <Radio value={1}>1</Radio>
                            <Radio value={2}>2</Radio>
                            <Radio value={3}>3</Radio>
                        </Space>
                </Radio.Group>
        </Modal>
        
    </div>
}
  
export default Service;