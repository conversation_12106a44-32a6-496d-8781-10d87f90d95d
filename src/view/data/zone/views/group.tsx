import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, Row, Col, Typography, Image, Dropdown, Menu, Button,Avatar, Badge } from 'antd'
import { useState } from 'react';
import { DragOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
const { Paragraph } = Typography
const formatGroup = (member, _type) => {
    if (_type === "visibility"){
        if (member.visibility == "ADMIN") return  "未开通管理员可见";
        if(member.visibility == "ALL")  return "未开通全员可见";
        if(member.visibility == "NO_VISIBILITY")  return "未开通均不可见";
    }
    if (_type === "tag") {
        if (member.tag == "RECOMMEND") return  "推荐";
        if(member.tag == "HOT")  return "火爆";
        if(member.tag == "EMPTY")  return "";
    }
    return "";
}
const Product = ({ product, groupIndex, tag, visibility }) => {
    const formatColor = (tag) => {
        if (tag) {
            if (tag == "火爆") return 'red';
            else return "blue";
        }
        else if (visibility) return "red";
        else return "blue";
    }
    const IMG_WIDTHS = 80;
    if (!tag) return (
        <div style={{ textAlign: "center" }}>
                <Image preview={false} src={product?.logoPc} width={IMG_WIDTHS} height={IMG_WIDTHS} />
                <br />
                {product?.name}
                <br />
                <h4 style={{ color: "rgb(196,141,78)" }}>
                ({visibility})
                </h4>
            </div>
    );
    return (
        <Badge.Ribbon text={tag} color={formatColor(tag)}>
            <div style={{ textAlign: "center" }}>
                    <Image preview={false} src={product?.logoPc} width={IMG_WIDTHS} height={IMG_WIDTHS} />
                    <br />
                    {product?.name}
                    <br />
                    <h4 style={{ color: "rgb(196,141,78)" }}>
                    ({visibility})
                    </h4>
                </div>
        </Badge.Ribbon>
    )
}
const Group = ({ 
        group, 
        groupIndex,
        onGroupDelete,
        onGroupNameEdit,
        onSwitchGroups,
        onShowProductAttach, 
        onShowProductTag, 
        onShowProductVisible, 
        onShowProductDelete,
    }) => {
    return (
        <div 
            title='拖动以调整分组位置'
            id={group.step}   
            draggable 
            onDragStart={(e) => {
                //@ts-ignore
                e.dataTransfer.setData('source',JSON.stringify( {
                    groupName:group.groupName,
                    seq:group.seq
                }));
            }}
            onDragOver={(e)=>{
                e.preventDefault()
            }}
            onDrop={(e)=>{
               const sourceStr = e.dataTransfer.getData('source')
               const source = JSON.parse(sourceStr)
                const dest = {
                    groupName:group.groupName,
                    seq:group.seq
                }
                onSwitchGroups(source,dest)
            }}
            style={{ border: "1px solid #ccc", padding: 20, marginBottom: 60 }}>
            <Row>
                <Col flex="30px">
                    <DragOutlined  translate={""} style={{ flex: "0 0 24px",cursor:"pointer" }} />
                </Col>
                <Col flex="auto">
                    <Paragraph  style={{ flex: "0 0 auto" }} editable={{
                        onChange: (v) => {
                            onGroupNameEdit(group,v)
                        }
                    }}>{group.groupName}</Paragraph>
                </Col>
                <Col flex="60px">
                    <Popconfirm onConfirm={()=>onGroupDelete(group)} title={()=>{
                        return <>
                            <h3>{`您确认要删除【${group.groupName}】分组吗?`}</h3>
                            <br/>
                            删除后，此分组中的服务将会被移除，但可重新添加至其他分组
                        </>
                    }} >
                        <DeleteOutlined translate={""} />
                    </Popconfirm>
                </Col>
            </Row>
            <Row gutter={60} align="middle">
                {group.members.map((member, index) => (
                    <Col key={`service${index}`}>
                        <Dropdown overlay={(
                            <Menu onClick={({key})=>{
                                switch (key) {
                                    case "visible":
                                        onShowProductVisible(group,member.product)
                                        break;
                                    case "tag":
                                        onShowProductTag(group,member.product)
                                        break;
                                    case "delete":
                                        onShowProductDelete(group,member.product)
                                        break;
                                }
                            }} >
                                <Menu.Item key="visible">
                                    修改可见性
                                </Menu.Item>
                                <Menu.Item key="tag">
                                    设置标签
                                </Menu.Item>
                                <Menu.Item key="delete">
                                    删除
                                </Menu.Item>
                            </Menu>
                        )} trigger={["hover"]}>
                            <a href="void(0)" onClick={e => e.preventDefault()} style={{ color: "inherit" }}>
                                <Product 
                                    groupIndex={groupIndex}
                                    product={member.product} 
                                    tag={formatGroup(member,'tag')}
                                    visibility={formatGroup(member,'visibility')}
                                />
                            </a>
                        </Dropdown >
                    </Col>
                ))}
                <Col>
                    <Button onClick={()=>{
                        onShowProductAttach(group)
                    }} type="primary" shape="circle" icon={<PlusOutlined translate={""} />} />
                </Col>
            </Row>
        </div>
    )
}

export default Group