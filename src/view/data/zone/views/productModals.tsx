import React, { useState } from 'react'
import { Modal, Checkbox, Radio, Card, Row, Col } from 'antd'
const filterAlreadyHasProducts = (group, products) => {
  const alreadyHasProductIds = group.members.reduce((v, t) => {
    v.push(t.productId);
    return v;
  }, []);
  return products.filter(item => !alreadyHasProductIds.includes(item.id));
}

export const ProductAttachModal = ({ group,product, products, shown, onProductsSelect, onCanCel }) => {
  if(!shown) {
    return null
  }
  let checked = [];
  const onCheckedHandler = (v) => {
    checked = v;
  }
  const doNotHasProducts = filterAlreadyHasProducts(group, products);
  return <Modal title="添加服务" visible={shown} onOk={() => {
    onProductsSelect(group, products, checked);
  }} onCancel={()=>onCanCel()}>
    <Checkbox.Group style={{ width: '100%' }} onChange={onCheckedHandler}>
         <Row>
          {
            doNotHasProducts.map(product => (
              <Col span={8} key={product.id}>
                <Checkbox  value={product.id}>{ product.name }</Checkbox>
              </Col>
            ))
          }
       </Row>
  </Checkbox.Group>
  </Modal>
}
export const ProductVisibleModal = ({ group,product, shown, onProductVisibleSelect,onCanCel }) => {
  if(!shown) {
    return null
  }
  const visibility = group.members.find(({ productId }) => productId == product?.id)?.visibility;
  let cv = null;
  return <Modal
    title={'修改可见性'}
    visible={shown} onOk={() => {
    onProductVisibleSelect(group, product, cv)
  }}   onCancel={() => onCanCel()}>
    <Radio.Group
      defaultValue={visibility}
      onChange={(v) => {
      //@ts-ignore
        cv = v;
    }}
    >
      <Radio value={'ADMIN'}>未开通管理员可见</Radio>
      <Radio value={'ALL'}>未开通全员可见</Radio>
      <Radio value={'NO_VISIBILITY'}>未开通均不可见</Radio>
    </Radio.Group>
  </Modal>
}
export const ProductTagModal = ({ group, product, shown, onProductTagSelect,onCanCel }) => {
  if(!shown) {
    return null
  }
  const tag = group.members.find(({ productId }) => productId == product.id)?.tag;
  let cv = null;
  return <Modal
    title={'设置标签'}
    visible={shown} onOk={() => {
      onProductTagSelect(group, product, cv)
  }} onCancel={()=>onCanCel()}>
    <Radio.Group
      defaultValue={tag}
      onChange={(v) => {
        //@ts-ignore
        cv = v;
    }} >
      <Radio value={'RECOMMEND'}>推荐</Radio>
      <Radio value={'HOT'}>火爆</Radio>
      <Radio value={'EMPTY'}>无</Radio>
    </Radio.Group>
  </Modal>
}
export const ProductDeleteModal = ({group, product, shown, onProductDelete, onCancel }) => {
  if(!shown) {
    return null
  }
  return <Modal onOk={() => {
    onProductDelete(group,product)
  }} onCancel={() => onCancel()} visible={shown} title={`您确认要删除【${product.name}】服务吗？`}>
    删除后，在三方专区中将不会显示此服务，之前已开通的服务可在已开通中查看。
  </Modal>
}