import React, { useContext, useEffect, useState } from 'react';

import { Button, Form, Input, Upload, Radio, message } from 'antd';
import { ApolloSimpleFormStage, ApplicationContext, withForm } from '@lanmaoly/admin-framework';
import { queryServiceManagemenetListById, updateServiceManagemenetListById, addServiceManagemenetList } from '../../../graphql';
import { enviroment } from "../../../env";

const uploadUri = enviroment.domain + "/api/archive/upload";
const { TextArea } = Input;

const ServiceNameField = withForm(Input, {
    label: "服务名称",
    name: ["name"],
    rules: [{ required: true, message: "服务名称不能超过15个字", max: 15 }]
});
const SysCodeField = withForm(Input, {
    label: "服务编码",
    name: ["sn"],
    rules: [{ required: true, message: "服务编码不能超过40个字", max: 40 }]
});
const ShortDespField = withForm(Input, {
    label: "一句话描述",
    name: ["description"],
    rules: [{ required: true, message: "一句话描述不能超过30个字", max: 30 }]
});
const ContactField = withForm(Input, {
    label: "联系方式",
    name: ["tel"],
    rules: [{ message: "联系方式不能超过20个字", max: 20 }]
});
const SupplierName = withForm(Input, {
    label: "供应商名称",
    name: ["supplierName"],
    rules: [{ message: "供应商名称不能超过10个字", max: 10 }]
});

const Type = withForm(
    (props) => {
        return <Radio.Group   {...props}>
            <Radio value={'OWN_BRAND'}>自有</Radio>
            <Radio value={'THIRD_PARTY'}>第三方</Radio>
        </Radio.Group>;
    },
    {
        label: "服务所属类型",
        name: ["type"],
        rules: [{ required: true }]
    });
const UseType = withForm(
    (props) => {
        return <Radio.Group {...props} >
            <Radio value={'CONVERGED_SOLUTIONS'}>方式一：融合解决方案方式</Radio>
            <Radio value={'PLATFORM_ACCESS'}>方式二：平台接入方式</Radio>
            <Radio value={'SERVICE_DISPLAY'}>方式三：服务展示方式</Radio>
        </Radio.Group>;
    },
    {
        label: "服务接入方式",
        name: ["useType"],
        rules: [{ required: true }]
    });
const OpenType = withForm(
    (props) => {
        return <Radio.Group {...props} >
            <Radio value={'DEFAULT'}>默认开通</Radio>
            <Radio value={'CLICK_TRY'}>点击试用即开通</Radio>
            <Radio value={'OFFLINE_AUDIT'}>线下审核开通</Radio>
        </Radio.Group>;
    },
    {
        label: "服务开通方式",
        name: ["openType"],
        rules: [{ required: true }]
    });
const Remark = withForm(
    (props) => {
        return <TextArea
            {...props}
            rows={4}
            maxLength={1000}
            placeholder={'请输入内容,不得超过1000字'}
            style={{ height: 120 }}
        >
            <br />
            <br />
        </TextArea>;
    },
    {
        label: "备注",
        name: ["remark"],
        rules: [{ max: 1000 }]
    });
const formatDefaultImgs = (imgUrls) => {
    if (!imgUrls || (!imgUrls.length)) return {};
    return imgUrls.reduce((v, t, i) => {
        const match = t.match(/(?<=\/)[^\/]+$/g);
        // const tStr =  t.slice(0, t.lastIndexOf('/'));
        // const finalStr =  tStr.slice(tStr.lastIndexOf('/') + 1);
        if (match != null) {
            //@ts-ignore
            const name = match[0];
            //@ts-ignore
            v.push({
                uid: Math.random(),
                name,
                status: 'done',
                url: t,
            });
            return v;
        } else {
            return v;
        }
    }, []);
}
const BasicInfo = () => {
    const [, option] = useContext(ApplicationContext);
    const id = option.match!.params.id;
    const [actionUrl] = useState(uploadUri);
    const [logoPcImgUrl, setIogoPcImgUrl] = useState("");
    const [logoH5ImgUrl, setIogoH5ImgUrl] = useState("");
    const [defaultPicturePcImgUrls, setDefaultPicturePcImgUrls] = useState([]);
    const [defaultPictureH5ImgUrls, setDefaultPictureH5ImgUrls] = useState([]);
    const [logoPcImgUrlLoading, setLogoPcImgUrlLoading] = useState(false)
    const [logoH5ImgUrlLoading, setLogoH5ImgUrlLoading] = useState(false)
    const [defaultPicturePcImgUrlsLoading, setDefaultPicturePcImgUrlsLoading] = useState(false)
    const [defaultPictureH5ImgUrlsLoading, setDefaultPictureH5ImgUrlsLoading] = useState(false)

    const [init, setInit] = useState(false);
    const isAdd = !!(id == null);
    return <ApolloSimpleFormStage
        id={id}
        query={queryServiceManagemenetListById}
        mutation={isAdd ? addServiceManagemenetList : updateServiceManagemenetListById}
        dataPath={(data) => {
            const datas = data.appstore.productInfo;
            if (!init) {
                if (!logoPcImgUrl) setIogoPcImgUrl(datas.logoPc);
                if (!logoH5ImgUrl) setIogoH5ImgUrl(datas.logoH5);
                //@ts-ignore
                if (defaultPicturePcImgUrls && (!defaultPicturePcImgUrls.length)) {
                    setDefaultPicturePcImgUrls(formatDefaultImgs(datas.picturePc));
                }
                if (defaultPictureH5ImgUrls && (!defaultPictureH5ImgUrls.length)) {
                    setDefaultPictureH5ImgUrls(formatDefaultImgs(datas.pictureH5));
                }
                setInit(true);
            }
            // type 服务所述类型
            return {
                ...datas
            };
        }}
        onSubmit={(values, mutation) => {
            if (!logoPcImgUrl || !logoH5ImgUrl || (!defaultPicturePcImgUrls.length) || (!defaultPictureH5ImgUrls.length)) return message.error("请上传所需文件!!!");
            mutation({
                variables: {
                    ...values,
                    id,
                    logoPc: logoPcImgUrl,
                    logoH5: logoH5ImgUrl,
                    //@ts-ignore
                    picturePc: defaultPicturePcImgUrls.map(item => item.url),
                    //@ts-ignore
                    pictureH5: defaultPictureH5ImgUrls.map(item => item.url),
                }
            })
        }}>
        <ServiceNameField required />
        <SysCodeField required />
        <ShortDespField required />
        <ContactField />
        <Form.Item label="pc端logo" required>
            <Upload multiple={true} name="file" action={actionUrl} onChange={(info) => {
                setLogoPcImgUrlLoading(true)
                if (!info.fileList.length) setIogoPcImgUrl("");
                if (info.file.status == "done") {
                    setLogoPcImgUrlLoading(false)
                    const logoPcImgUrl = info.file.response.data.url;
                    setIogoPcImgUrl(logoPcImgUrl);
                }
            }}>
                <Button loading={logoPcImgUrlLoading}>选择文件</Button>
            </Upload>
            <img src={logoPcImgUrl} width={100} height={100} />
        </Form.Item>
        <Form.Item label="移动端logo" required>
            <Upload multiple={true} name="file" action={actionUrl} onChange={(info) => {
                setLogoH5ImgUrlLoading(true)
                if (!info.fileList.length) setIogoH5ImgUrl("");
                if (info.file.status == "done") {
                    setLogoH5ImgUrlLoading(false)
                    const IogoH5ImgUrl = info.file.response.data.url;
                    setIogoH5ImgUrl(IogoH5ImgUrl);
                }
            }}>
                <Button loading={logoH5ImgUrlLoading}>选择文件</Button>
            </Upload>
            <img src={logoH5ImgUrl} width={100} height={100} />
        </Form.Item>
        <Form.Item label="pc端服务详情" required>
            <Upload
                fileList={defaultPicturePcImgUrls}
                //@ts-ignore
                defaultFileList={defaultPicturePcImgUrls}
                //  @ts-ignore
                name="file" action={actionUrl} onChange={(info) => {
                    setDefaultPicturePcImgUrlsLoading(true)
                    if (info.file.status == "done") {
                        setDefaultPicturePcImgUrlsLoading(false)
                        if (!info.fileList.length) setDefaultPicturePcImgUrls([]);
                        const files = info.fileList.filter(item => item.response != null);
                        // console.log('files', files  );
                        // @ts-ignore
                        const picturePcImgUrls = files.reduce((v, t) => [...v, t.response.data.url], [])
                        //@ts-ignore
                        setDefaultPicturePcImgUrls([...defaultPicturePcImgUrls, ...formatDefaultImgs(picturePcImgUrls)]);
                    } else {
                        if (info.file.status != "removed") return;
                        console.log('remove remove');
                        if (!info.fileList.length) setDefaultPicturePcImgUrls([]);
                        const filteredPicturePcImgUrls = defaultPicturePcImgUrls.reduce((v, t) => {
                            //@ts-ignore
                            if (info.fileList.find(fileItem => {
                                //@ts-ignore
                                return fileItem.url === t.url;
                            }))
                                v.push(t);
                            return v;
                        }, [])
                        //@ts-ignore
                        setDefaultPicturePcImgUrls(filteredPicturePcImgUrls);
                    }
                }}>
                <Button loading={defaultPicturePcImgUrlsLoading}>选择文件</Button>
            </Upload>
            {
                defaultPicturePcImgUrls?.map((url, index) =>
                    //@ts-ignore
                    (<img key={index} src={url.url} width={100} height={100} />)
                )
            }
        </Form.Item>
        <Form.Item label="移动端服务详情" required>
            <Upload
                fileList={defaultPictureH5ImgUrls}
                name="file" action={actionUrl} onChange={(info) => {
                    setDefaultPictureH5ImgUrlsLoading(true)
                    if (info.file.status == "done") {
                        setDefaultPictureH5ImgUrlsLoading(false)
                        if (!info.fileList.length) setDefaultPictureH5ImgUrls([]);
                        const files = info.fileList.filter(item => item.response != null);
                        // @ts-ignore
                        const pictureH5ImgUrls = files.reduce((v, t) => [...v, t.response.data.url], [])
                        //@ts-ignore
                        setDefaultPictureH5ImgUrls([...defaultPictureH5ImgUrls, ...formatDefaultImgs(pictureH5ImgUrls)]);
                    } else {
                        if (info.file.status != "removed") return;
                        if (!info.fileList.length) setDefaultPictureH5ImgUrls([]);
                        const filteredPictureH5ImgUrls = defaultPictureH5ImgUrls.reduce((v, t) => {
                            //@ts-ignore
                            if (info.fileList.find(fileItem => {
                                //@ts-ignore
                                return fileItem.url === t.url;
                            }))
                                v.push(t);
                            return v;
                        }, [])
                        //@ts-ignore
                        setDefaultPictureH5ImgUrls(filteredPictureH5ImgUrls);
                    }
                }}>
                <Button loading={defaultPictureH5ImgUrlsLoading}>选择文件</Button>
            </Upload>
            {
                defaultPictureH5ImgUrls?.map((url, index) =>
                    //@ts-ignore
                    (<img key={index} src={url.url} width={100} height={100} />)
                )
            }
        </Form.Item>


        <Type />
        <SupplierName required />
        <UseType />
        <OpenType />
        <Remark />

    </ApolloSimpleFormStage>
};

export default () => {
    return <BasicInfo />
}
