import React, { useState } from "react";
import ServiceContent from "./ServiceContent";
import DragTopBar from "./DragTopBar";
import { Modal } from 'antd';
const DragItem: any = ({onDrag, data, index}) => {
    console.log('drag item', data, index);
    const [isModalOpen, setIsModalOpen] = useState(false);
    return <div style={{border: '1px solid black', padding: "5px", margin: '5px 10px'}}>
        <DragTopBar data={data} index={index} item={
            data?.appstore?.layoutInfo?.groups && 
            data?.appstore?.layoutInfo?.groups[index]
        } onEdit={() => { }} onDeleteGroup={() => {
            setIsModalOpen(true);
        }}></DragTopBar>
        <ServiceContent
            data={data} index={index}
            item={
            data?.appstore?.layoutInfo?.groups && 
            data?.appstore?.layoutInfo?.groups[index]
            }
        ></ServiceContent>
        {index}
          <Modal
                visible={isModalOpen}
                title="删除分组"
                onOk={() => {
                    setIsModalOpen(false);
                    if (data?.appstore?.layoutInfo?.groups) {
                        console.log('grups', data.appstore.layoutInfo.groups, index);
                        data.appstore.layoutInfo.groups = data.appstore.layoutInfo.groups.filter((e, i) => i != index);
                        var e = new Event("save-layout");
                        //@ts-ignore
                        e.data = data;
                        document.dispatchEvent(e);
                    }
                }}
                onCancel={() => {
                    setIsModalOpen(false);
                }}>
            <h3>您确认要删除【{ data?.appstore?.layoutInfo?.groups && data?.appstore?.layoutInfo?.groups[index]?.groupName }】吗？</h3>
            <p>删除后，在三方专区中将不会显示此服务，之前已开通的服务可在已开通中查看。</p>
        </Modal>
    </div>
}
  
export default DragItem;