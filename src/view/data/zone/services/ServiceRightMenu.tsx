import React, { useState } from "react";
import { List, Typography } from 'antd';
const ServiceRightMenu: any = ({ onChose, options, position = {x: 0, y: 0}}) => {
  const [background, setBackground] = useState("white");
    return <>
        <List
        style={{
          position: 'absolute',
          left: position.x, 
          top: position.y,
          background: 'white',
          zIndex: 100
      }}
      bordered
      dataSource={options}
      renderItem={(item:any) => (
        <List.Item
          style={{ ...item?.style ?? {}, width: 120, height: 30 }} onClick={() => onChose(item)}>
          <span
          style={{background}}
          //  onMouseEnter={(e) => {
          //   console.log(e)
          //   setBackground("gray");
          // }}
          // onMouseLeave={() => {
          //   setBackground("white");
          // }}
          >
            {item.content}
          </span>
        </List.Item>
      )}
    />
    </>
}
  
export default ServiceRightMenu;