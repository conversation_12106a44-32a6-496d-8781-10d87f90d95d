import React, {useState} from "react";
import Service from "../views/Service";
import ServiceContentAddBtn from "./ServiceContentAddBtn";
import { Modal,Radio,Space } from 'antd';
const formatServiceStatus = (serviceStatus) => {
    let state = "";
    if (serviceStatus == "ADMIN") {
        state = "未开通管理员可见";
    } else if (serviceStatus == "ALL") {
        state = "未开通全员可见";
    } else if (serviceStatus == "NO_VISIBILITY") {
        state = "未开通均不可见";
    }
    return `(${state})`;
}
const formatMemberTag = (serviceStatus) => {
    let state = "";
    console.log('service state', serviceStatus);
    if (serviceStatus == "RECOMMEND") {
        state = "推荐";
    } else if (serviceStatus == "HOT") {
        state = "火爆";
    } else if (serviceStatus == "EMPTY") {
        state = "无";
    } 
    return `${state}`;
}
const ServiceContent: any = ({item, index, data}) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [value, setValue] = useState(1);
    console.log('service content', item);
  const onChange = (e) => {
    console.log('radio checked', e.target.value);
    setValue(e.target.value);
  };
    return <div style={{
        display: 'flex',
        flexDirection:"row",
        justifyContent: "flex-start",
        alignItems: "center"
    }}>
        {
            item && item.members.map(member => (
                <Service onClick={() => {
                }}
                    status={formatServiceStatus(member?.visibility)}
                    title={member?.product?.name??''}
                    icon={member?.product?.logoPc ?? ''}
                    recommend={formatMemberTag(member?.tag??'')}
                />
                    
            ))
        }
        <ServiceContentAddBtn onClick={() => { 
            setIsModalOpen(true);
        }} />
          <Modal
                visible={isModalOpen}
                title="新增服务"
                onOk={() => {
                    setIsModalOpen(false);
                }}
                onCancel={() => {
                    setIsModalOpen(false);
                }}>
                    <Radio.Group onChange={onChange} value={value}>
                        <Space direction="vertical">
                            <Radio value={1}>1</Radio>
                            <Radio value={2}>2</Radio>
                            <Radio value={3}>3</Radio>
                        </Space>
                </Radio.Group>
        </Modal>
    </div>
}
  
export default ServiceContent;