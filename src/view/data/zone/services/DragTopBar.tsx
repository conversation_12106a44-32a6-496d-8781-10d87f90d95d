import React, { useState } from "react";
import { UnorderedListOutlined, EditOutlined } from '@ant-design/icons';
import { Input } from 'antd';
const DragTopBar: any = ({onEdit, onDeleteGroup, item, data, index}) => {
  console.log('datqa', item);
    const [edit, setEdit] = useState(false);
    return <div style={{display:'flex', justifyContent:'space-between', alignItems:'center'}}>
      <div style={{display:'flex', alignItems:"center"}}  onMouseLeave={() => {setEdit(false)}}>
        <UnorderedListOutlined translate={""}/>
        <Input value={item?.groupName} style={{ margin: "0 20px" }} 
          allowClear
          disabled={!edit}
          bordered={edit}
          onPressEnter={() => {
            setEdit(false);
          }}
        />
          <EditOutlined translate={""} onClick={() => {
            setEdit(true);
          }} />
      </div>
      <p style={{color:'red', cursor:'pointer'}} onClick={onDeleteGroup}>删除分组</p>
    </div>
}
  
export default DragTopBar;