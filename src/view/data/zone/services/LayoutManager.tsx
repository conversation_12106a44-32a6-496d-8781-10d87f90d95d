import React, {useEffect, useState} from "react";
import {queryLayoutInfoList} from '../../../../graphql';
import { message } from 'antd';
import { useApolloClient } from "@apollo/react-hooks";
import DragItemGroup from "./DragItemGroup";
import { Modal, Input } from 'antd';


const LayoutManager: any = () => {
    const client = useApolloClient();
    const [initFormValue, setInitFormValue] = useState({});
    /**
     * 保存更改
     */
    const saveLayoutHandler = (e) => {
        console.log("收到事件",e, initFormValue, e.data);
        setInitFormValue(() => e.data);
    }
    useEffect(() => {
        document.addEventListener("save-layout", saveLayoutHandler);
        return () => {
            document.removeEventListener("save-layout", saveLayoutHandler);
        }   
    }, [saveLayoutHandler]) 
    useEffect(() => {
        client.query({
        query: queryLayoutInfoList,
        }).then((ret) => {
            if (ret.data) {
                setInitFormValue(ret.data);
                console.log('dsddd', ret.data);
            }
            message.success("获取布局信息成功");
        });
    }, [client]);
    return <>
        <DragItemGroup data={initFormValue}></DragItemGroup>
    </>
}
  
export default LayoutManager;