import React, { useState } from "react";
import DragItem from "./DragItem";
import { Modal, Input } from 'antd';
import AddNewGroup from "./AddNewGroup";
const DragItemGroup: any = ({ data }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    return <div style={{ display: 'flex', flexDirection: 'column' }}>
        {
            data?.appstore?.layoutInfo?.groups?.map((item, index) => (
                <DragItem data={data} index={index}></DragItem>
            ))
        }
        <AddNewGroup onAddNewGroup={() => {
            setIsModalOpen(true);
        }}></AddNewGroup>
        <Modal
            visible={isModalOpen}
            title="新建分组"
            onOk={() => {
                setIsModalOpen(false);
            }}
            onCancel={() => {
                setIsModalOpen(false);
            }}>
            <div className="item" style={{
                display: 'flex',
                justifyContent: "space-between",
                alignItems: 'center'
            }}>
                <div className="left">
                    <span style={{ color: 'red' }}>*</span>
                    <span>分组名称</span>
                </div>
                <Input style={{ width: '400px' }} placeholder={'请输入分组名称'} />
            </div>
        </Modal>
    </div>
}

export default DragItemGroup;