import React from "react";
import { PlusOutlined } from '@ant-design/icons';
import Clickable from "../views/Clickable";
const AddNewGroup: any = ({onAddNewGroup}) => {
    return (
        <div style={{
            marginTop: "30px"
        }}>
            <PlusOutlined translate={""} style={{fontSize: 20, verticalAlign: 'middle'}} />
            <span style={{ color: 'rgb(165,203,224)', display: 'inline-block'}} onClick={onAddNewGroup}>添加新分组</span>
        </div>
    );
}
  
export default Clickable(AddNewGroup);