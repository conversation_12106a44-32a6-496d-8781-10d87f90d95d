import React, { useEffect, useState } from 'react';
import { Form, Modal, Input, Button, message } from 'antd';
import { queryLayoutInfoList,queryServiceManagemenetList, updateLayoutInfoList } from '../../../graphql';
import { useApolloClient } from "@apollo/react-hooks";
import Group from './views/group'
import {
  ProductAttachModal,
  ProductDeleteModal,
  ProductTagModal,
  ProductVisibleModal
} from './views/productModals'

var cgroup = null
var cproduct = null

const returnRightSeqOfLayouts = (layouts) => {
  layouts.groups.sort((a, b) => a.seq - b.seq);
  layouts.groups.forEach(group => {
    group.members.sort((a, b) => a.seq - b.seq);
  })
  return layouts;
}

const getMutationLayouts = (layouts) => {
  const deleteKey = (ls, ks) => {
    for (let key in ls) {
      if (key == "product") {
        delete ls[key];
      }
        if (ls[key] instanceof Object) {
          deleteKey(ls[key],ks);
        } else {
          if (key == ks) {
            delete ls[key];
          }
        }
      }
  }
  deleteKey(layouts, "__typename");
  
  return layouts;
}

const groupsWalk = (groups, fn)=>{
  for(var index in groups){
    const g = groups[index]
    const r = fn(g,index)
    if(!r){
      break
    }
  }
}
export default () => {
  const client = useApolloClient();
  const [newGroupShown, setNewGroupShown] = useState(false)
  const [productModalShown, setProductModalShown] = useState({
    attach: false,
    delete: false,
    tag: false,
    visible: false
  })
  const [layouts, setLayouts] = useState({
    groups: []
  });

  const [allProducts, setAllProducts] = useState([])

  const [reload, setReload] = useState(0)

  const [newGroupForm] = Form.useForm()



  const updateLayouts = (client, layouts) => {
    layouts = returnRightSeqOfLayouts(layouts);
    layouts =  getMutationLayouts(layouts);
    //把seq顺序给扭对
    client.mutate({
      mutation: updateLayoutInfoList,
      variables: {
        groups: layouts.groups,
      }
    }).then((ret) => {
      message.success("保存成功");
    }).catch((err) => {
      message.error("保存失败");
      console.warn('save error', err)
    }).finally(() => {
      setReload(Math.random());
    });
  }

  useEffect(() => {
    client.query({
      query: queryServiceManagemenetList,
      variables:{
        option:{
          "offset": 0,
          "limit": 1000000,
          "sorts": []
      }
      }
    }).then((ret) => {
        //@ts-ignore
        setAllProducts(ret.data.appstore.pageProductInfo.data)
    });
  }, [client])

  useEffect(() => {
    client.query({
      query: queryLayoutInfoList,
    }).then((ret) => {
      const layouts = {
        groups: ret.data.appstore.layoutInfo.groups
      };
      const rightLayouts = returnRightSeqOfLayouts(layouts);
      //@ts-ignore
      setLayouts(rightLayouts)
    });
  }, [client, reload])


  return (
    <>
      {layouts.groups.map((group, index) => {
        return (
          <Group
            group={group}
            groupIndex={index}
            key={index}
            onGroupDelete={(group) => {
               var n = {...layouts}
               for(var index in n.groups){
                const c = n.groups[index]
                //@ts-ignore
                if(c.groupName === group.groupName){
                  //@ts-ignore
                  n.groups.splice(index,1)
                  break
                }
               }
               updateLayouts(client,n)
            }}
            onGroupNameEdit={(group, newName) => {
              var n = { ...layouts }
              group.groupName = newName;
              updateLayouts(client, n);
            }}
            onSwitchGroups={(source:any,dest)=>{
              var n = {...layouts}
              var sindex = 0
              var dindex = 0
              groupsWalk(n.groups,(g,index)=>{
                if(g.groupName === source.groupName){
                  sindex = index
                  return false
                }
                return true
              })
              groupsWalk(n.groups,(g,index)=>{
                if(g.groupName === dest.groupName){
                  dindex = index
                  return false
                }
                return true
              })
              //@ts-ignore
              const t = n.groups[sindex].seq
              //@ts-ignore
              n.groups[sindex].seq = n.groups[dindex].seq
              //@ts-ignore
              n.groups[dindex].seq = t
              updateLayouts(client,n)
            }}
            onShowProductDelete={(group,product) => {
              cgroup = group
              cproduct = product
              setProductModalShown({ ...productModalShown, delete: true })
            }}
            onShowProductAttach={(group,product) => {
              cgroup = group
              cproduct = product
              setProductModalShown({ ...productModalShown, attach: true })
            }}
            onShowProductTag={(group,product) => {
              cgroup = group
              cproduct = product
              setProductModalShown({ ...productModalShown, tag: true })
            }}
            onShowProductVisible={(group,product) => {
              cgroup = group
              cproduct = product
              setProductModalShown({ ...productModalShown, visible: true })
            }}

          />
        )
      })}
      <div>
        <Button type="primary" onClick={() => setNewGroupShown(true)}>添加分组</Button>
      </div>
      <Modal
        visible={newGroupShown}
        title="新建分组"
        onOk={() => {
          newGroupForm
            .validateFields()
            .then(values => {
              console.log("create group", values)
              var n = { ...layouts };
              //@ts-ignore
              const newSeq = n.groups.reduce((v, t) => t.seq > v ? t.seq : v, 0) + 1;
              //@ts-ignore
              const newGroup = {
                groupName: values?.name ?? "",
                seq: newSeq,
                members: [],
              };
              //@ts-ignore
              n.groups.push(newGroup);
              updateLayouts(client, n);
              setNewGroupShown(false);
            })
            .catch(info => {
              console.log('Validate Failed:', info);
            }).finally(() => {
              newGroupForm.resetFields();
            })
        }}
        onCancel={() => {
          setNewGroupShown(false);
          newGroupForm.resetFields();
        }}>
        <Form form={newGroupForm}
        >
          <Form.Item name="name" label="分组名称" rules={[{ required: true, message: "请输入分组名称" }, {  message: "最大值为20个字符" , max: 20 }]}>
            <Input placeholder="请输入名称" />
          </Form.Item>
        </Form>
      </Modal>
      <ProductAttachModal
        product={cproduct}
        group={cgroup}
        products={allProducts}
        shown={productModalShown.attach}
        onProductsSelect={(group, products, checked) => {
          const checkedProducts = products.filter(({ id }) => checked.includes(id));
          var n = { ...layouts }
          //@ts-ignore
          const Igroup = n.groups.find(({ groupName, seq }) => groupName + seq == group.groupName + group.seq);
          if (Igroup) {
            //@ts-ignore
            const members = Igroup.members;
            let maxIndex = members.reduce((v,t) => t .seq> v ? t.seq : v ,0);
            //@ts-ignore
            checkedProducts.forEach((item,index) => {
              //@ts-ignore
              Igroup.members.push({
                seq: maxIndex +  1 + index,
                tag:  "EMPTY",
                visibility:  "ADMIN" ,
                product: item,
                productId: item.id,
              });
            });
          }
          updateLayouts(client, n);
        setProductModalShown({...productModalShown,attach:false})
      }}
      onCanCel={()=>setProductModalShown({...productModalShown,attach:false})}
      />
      <ProductVisibleModal
        group={cgroup}
        product={cproduct}
        shown={productModalShown.visible}
        onCanCel={() => setProductModalShown({ ...productModalShown, visible: false })}
        onProductVisibleSelect={(group, product, v) => { 
        var n = {...layouts}
        for(var item of n.groups){
          //@ts-ignore
          if(item.groupName !== group.groupName){
            continue
          }
          //@ts-ignore
          for(var index in item.members){
            //@ts-ignore
            const member = item.members[index];
            //@ts-ignore
            const p = item.members[index].product
            if(p.id === product.id){
              //@ts-ignore
              member.visibility = v.target.value;
              break;
            }
          }
        }
        updateLayouts(client, n);
        setProductModalShown({...productModalShown,visible:false})
      }}  />
      <ProductTagModal
        group={cgroup}
        product={cproduct}
        shown={productModalShown.tag}
        onCanCel={() => setProductModalShown({ ...productModalShown, tag: false })}
        onProductTagSelect={(group, product, v) => { 
          var n = {...layouts}
          for(var item of n.groups){
            //@ts-ignore
            if(item.groupName !== group.groupName){
              continue
            }
            //@ts-ignore
            for(var index in item.members){
              //@ts-ignore
              const member = item.members[index];
              //@ts-ignore
              const p = item.members[index].product
              if(p.id === product.id){
                //@ts-ignore
                member.tag = v.target.value;
                break;
              }
            }
          }
          updateLayouts(client, n);
        setProductModalShown({...productModalShown,tag:false})
      }} />
      <ProductDeleteModal 
        group={cgroup}
        product={cproduct} 
        shown={productModalShown.delete} 
        onProductDelete={(group,product) => {
          var n = {...layouts}
          for(var item of n.groups){
            //@ts-ignore
            if(item.groupName !== group.groupName){
              continue
            }
            //@ts-ignore
            for(var index in item.members){
              //@ts-ignore
              const p = item.members[index].product
              if(p.id === product.id){
                //@ts-ignore
                item.members.splice(index,1)
                break
              }
            }
          }
          updateLayouts(client,n)
          setProductModalShown({...productModalShown,delete:false})
        }}
        onCancel={()=>setProductModalShown({...productModalShown,delete:false})} 
      />
    </>
  )
}