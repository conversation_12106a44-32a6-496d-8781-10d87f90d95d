import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Form,
  Input,
  Button,
  Select,
  Row,
  Col,
  DatePicker,
  Table,
  Tooltip,
  message
} from "antd";
import axios from "axios";
import './css/index.css'
import {enviroment} from '../../../env'
const { RangePicker } = DatePicker;
const OperationLog = () => {
  const columns = [
    {
      title: "操作时间",
      dataIndex: "operateTime",
      key: "operateTime",
      ellipsis: {
        showTitle: false,
      },
      render: (operateTime) => (
        <Tooltip title={operateTime} placement="topLeft">
          {operateTime.slice(0, 16)}
        </Tooltip>
      ),
    },
    {
      title: "企业",
      dataIndex: "merchantName",
      key: "merchantName",
      ellipsis: {
        showTitle: false,
      },
      render: (merchantName) => (
        <Tooltip title={merchantName} placement="topLeft">
          {merchantName}
        </Tooltip>
      ),
    },
    {
      title: "操作人",
      dataIndex: "userName",
      key: "userName",
      ellipsis: {
        showTitle: false,
      },
      render: (userName) => (
        <Tooltip title={userName} placement="topLeft">
          {userName.split('(')[0] || '--'}
        </Tooltip>
      ),
    },
    {
      title: "操作名称",
      dataIndex: "operateName",
      key: "operateName",
    },
    {
      title: "操作详情",
      dataIndex: "operateDesc",
      ellipsis: {
        showTitle: false,
      },
      render: (operateDesc) => (
        <Tooltip title={operateDesc} placement="topLeft" overlayClassName={'tooltip'}>
          <p className="showOverTooltip">{operateDesc}</p>
        </Tooltip>
      ),
      key: "operateDesc",
    },
    {
      title: '操作结果',
      dataIndex: 'bizResult',
      width: 120,
      key: 'bizResult',
      render: (bizResult, record) => {
        return (
          <Tooltip title={bizResult === 'SUCCESS' ? '' : record.failReason} placement="topLeft">
            <span className={bizResult !== 'SUCCESS' ? 'failFont' : ''}>
              {bizResult === 'SUCCESS' ? '操作成功' : bizResult === 'FAIL' ? '操作失败' : '操作异常'}
            </span>
          </Tooltip>
        )
      },
    },
    // {
    //   title: "应用模块",
    //   dataIndex: "applicationName",
    //   key: "applicationName",
    //   render:(_,row) => {
    //     if (!row.menu1st) {
    //       return row.applicationName
    //     } else if (!row.menu2nd) {
    //       return `${row.applicationName}/${row.menu1st}`
    //     } else {
    //       return `${row.applicationName}/${row.menu1st}/${row.menu2nd}`
    //     }
    //   }
    // },
  ];
  let [application, setApplication] = useState([]);
  let [logTableList, setLogTable] = useState([]);
  let [searchParams] = useState({});
  let [loading, setLoading] = useState(false);
  let [pagination, setPageParams] = useState({
    current: 0,
    pageSize: 0,
    total: 0,
  });
  const searchRef = useRef(null);
  const [form] = Form.useForm();
  const defaultAppIp = enviroment.domain + "/api/operate/application/enum";
  const defaultLogIp =  enviroment.domain + "/api/operate/list";
  const status = [
    { name: '全部', code: '' },
    { name: '操作成功', code: 'SUCCESS' },
    { name: '操作失败', code: "FAIL" },
    { name: "操作异常", code: 'EXCEPTION' }
  ]
  // 应用模块
  const getApplication = () => {
    fetch(defaultAppIp)
      .then((res) => res.json())
      .then((res: any) => {
        setApplication(res.data);
      });
  };
  // 获取日志列表数据
  const getLogTableList = (data) => {
    setLoading(true);
    const token = window.sessionStorage.getItem("token");
    axios
      .post(defaultLogIp, data, { headers: { Authorization: token } })
      .then((res) => {
        setLogTable(res.data.data.records);
        setLoading(false);
        setPageParams({
          current: res.data.data.current,
          total: res.data.data.total,
          pageSize: data.pageSize,
        });
      });
  };
  useEffect(() => {
    getApplication();
    getLogTableList(searchParams);
  }, [searchParams]);
  // 提交搜索表单
  const onFinishForm = (values: any) => {
    handleParams(values, pagination.pageSize, 1);
  };
  const handleParams = (values, pageSize, current) => {
    for (let key in values) {
      if (!values[key]) {
        values[key] = "";
      }
      if (key === "operateTime" && values["operateTime"]) {
        const operateDateStart = handleTimeParams(values["operateTime"][0]);
        const operateDateEnd = handleTimeParams(values["operateTime"][1]);
        values["operateDateStart"] = operateDateStart;
        values["operateDateEnd"] = operateDateEnd;
        delete values["operateTime"];
      }
    }
    values["pageSize"] = pageSize;
    values["currentPage"] = current;
    getLogTableList(values);
  };
  // 处理时间数组
  const handleTimeParams = (time) => {
    const { _d } = time;
    let date = new Date(_d);
    const y = date.getFullYear();
    let m: string | number = date.getMonth() + 1;
    m = m < 10 ? "0" + m : m;
    let d: string | number = date.getDate();
    d = d < 10 ? "0" + d : d;
    return y + "-" + m + "-" + d;
  };
  // 清除表单
  const clearInputVal = () => {
    // @ts-ignore
    searchRef.current.resetFields();
  };
  // 处理分页
  const handleTableChange = async (pagination) => {
    const values = await form.validateFields();
    handleParams(values, pagination['pageSize'], pagination['current']);
  };
  const onTableShow = useCallback((row) => {
    return {
      onMouseEnter: () => {
        // toastTips(row)
      }
    }
  }, [])
  //移入显示toast
  const toastTips = (record) => {
    if (record.bizResult !== 'SUCCESS') {
      message.destroy()
      message.error(record.failReason)
    }
  }
  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  // @ts-ignore
  return (
    <div>
      <Form onFinish={onFinishForm} ref={searchRef} form={form} className='form'>
        <Row gutter={16}>
          <Col className="gutter-row" span={7}>
            <Form.Item label="操作时间" name="operateTime" colon={false}>
              <RangePicker />
            </Form.Item>

          </Col>
          <Col className="gutter-row" span={7}>
            <Form.Item label="操作人" name="userName" colon={false}>
              <Input placeholder="请输入操作人" allowClear/>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={7}>
            <Form.Item label="操作名称" name="operateName" colon={false}>
              <Input placeholder="请输入操作名称" allowClear/>
            </Form.Item>

          </Col>
        </Row>
        {/* <Row gutter={16}>
          <Col className="gutter-row" span={7}>
            <Form.Item label="应用模块" name="application" colon={false}>
              <Select placeholder='请选择应用模块'>
                {
                  application.map((item) => {
                    // @ts-ignore
                    return <Select.Option value={item.code} key={item.code}>{item.name}</Select.Option>
                  })
                }
              </Select>
            </Form.Item>

          </Col>
          <Col className="gutter-row" span={7}>
            <Form.Item label="一级菜单" name="menu1st" colon={false}>
              <Input placeholder="请输入一级菜单" allowClear/>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={7}>
            <Form.Item label="二级菜单" name="menu2nd" colon={false}>
              <Input placeholder="请输入二级菜单" allowClear/>
            </Form.Item>
          </Col>
        </Row> */}
        <Row gutter={16}>
          <Col className="gutter-row" span={6}>
            <Form.Item label="企业" name="merchantName" colon={false}>
              <Input placeholder="请输入企业" allowClear/>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={6}>
            <Form.Item label="操作详情" name="operateDesc" colon={false}>
              <Input placeholder="请输入操作详情" allowClear/>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={6}>
            <Form.Item label="操作结果" name="bizResult" colon={false}>
              <Select defaultValue={'全部'}>
                {
                  status.map((item) => {
                    // @ts-ignore
                    return <Select.Option value={item.code} key={item.code}>{item.name}</Select.Option>
                  })
                }
              </Select>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={2} style={{ marginLeft: '20px' }}>
            <Form.Item>
              <Button type="primary" htmlType={"submit"}>
                查询
              </Button>
            </Form.Item>
          </Col>
          <Col className="gutter-row" span={2}>
            <Form.Item>
              <Button onClick={clearInputVal}>重置</Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Table
        onRow={onTableShow}
        loading={loading}
        scroll={{ x: 1500, y: 500 }}
        dataSource={logTableList}
        columns={columns}
        rowKey={(record) => record.id}
        onChange={handleTableChange}
        pagination={pagination}
      />
    </div>
  );
};

export default () => <OperationLog />;
