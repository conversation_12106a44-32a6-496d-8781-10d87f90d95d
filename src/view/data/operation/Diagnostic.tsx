import React, { useState, useEffect, useRef, useCallback } from "react";
import CryptoJ<PERSON> from "crypto-js"
import { enviroment } from '../../../env';
import { Button, Input, Form, Switch, Radio } from 'antd'

const key = CryptoJS.enc.Base64.parse("o9szYIOq1rRMiouNhNvaq96lqUvCekxR");

function encrypt(key: CryptoJS.lib.WordArray, word: string) {
  var srcs = CryptoJS.enc.Utf8.parse(word);
  var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  return encrypted.toString();
}

function decrypt(key: CryptoJS.lib.WordArray, word: string) {
  var decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  return CryptoJS.enc.Utf8.stringify(decrypt).toString();
}

function ls(param: any): Promise<string> {
  const api = enviroment.domain + "/api/diag/l";

  let q = encrypt(key, JSON.stringify(param))
  let form = new FormData()
  form.set("q", q)
  return fetch(api, {
    method: 'POST',
    body: form
  }).then(r => r.text()).then(r => {
    return r.split("\r\n").map(line => decrypt(key, line)).join("\r\n")
  })
}

function grep(param: any): Promise<string> {
  const api = enviroment.domain + "/api/diag/g";

  let q = encrypt(key, JSON.stringify(param))
  let form = new FormData()
  form.set("q", q)
  return fetch(api, {
    method: 'POST',
    body: form
  }).then(r => r.text()).then(r => {
    return r.split("\r\n").map(line => decrypt(key, line)).join("\r\n")
  })
}

const Diagnostic = () => {

  const [loading, setLoading] = useState(false)
  const div = useRef<HTMLPreElement>(null)

  const [form] = Form.useForm()

  return (
    <div>
      <div>
        <Form size="small" initialValues={{
        }} form={form} layout="inline">
          <Form.Item name="path">
            <Input placeholder="路径"></Input>
          </Form.Item>
          <Form.Item name="pattern">
            <Input placeholder="正则"></Input>
          </Form.Item>
          <Form.Item name="before">
            <Input style={{
              width: "60px"
            }} type="number" placeholder="前"></Input>
          </Form.Item>
          <Form.Item name="after">
            <Input style={{
              width: "60px"
            }} type="number" placeholder="后"></Input>
          </Form.Item>
          <Form.Item name="gzip" label="gzip">
            <Switch></Switch>
          </Form.Item>
          <Button.Group>
            <Button type="primary" loading={loading} onClick={() => {
              const v = form.getFieldsValue()
              setLoading(true)
              grep(v).then(s => {
                if (div.current) {
                  div.current.innerText = s
                }
              }).finally(() => setLoading(false))
            }}>搜索</Button>
            <Button loading={loading} onClick={() => {
              const v = form.getFieldsValue()
              setLoading(true)
              ls(v).then(s => {
                if (div.current) {
                  div.current.innerText = s
                }
              }).finally(() => setLoading(false))
            }}>列表</Button>
          </Button.Group>
        </Form>
      </div>
      <div style={{
        height: "500px",
        overflowY: "auto"
      }}>
        <pre ref={div}></pre>
      </div>
    </div>
  );
};

export default () => <Diagnostic />;
