
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryWebsiteConfigInfos} from '../../../graphql';


const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})



export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100
  }, {
    title: '名称',
    dataIndex: 'merchantName',
  }, {
    title: '网站域名',
    dataIndex: 'domainName',
  }, {
    title: '创建日期',
    dataIndex: 'createTime',
  }]

  const stage = useQueryStage({
    query: queryWebsiteConfigInfos,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.configInfo.websiteConfigInfos
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/website/config/edit/${record.id}`)
    }
  }]


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/website/config/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}