import React, {useContext, useState,useEffect} from 'react';

import {Button, Form, Input, Upload,message} from 'antd';
import {ApolloSimpleFormStage, ApplicationContext, withForm} from '@lanmaoly/admin-framework';
import {queryAppletsConfigInfo, updateAppletsConfigInfo} from '../../../graphql';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {enviroment} from "../../../env";

const uploadUri = enviroment.domain+"/api/archive/upload";

const MerchantNameField = withForm(Input, {
    label: "小程序名称",
    name: ["name"],
    rules: [{required: true, message: "请输入"}]
});

const SloganField = withForm(Input, {
    label: "页面提示语",
    name: ["prompt"],
});

const DomainNameField = withForm(Input, {
    label: "用户协议名称",
    name: ["agreementName"],
});

const NetworkSecurityField = withForm(Input, {
    label: "备注",
    name: ["remark"],
});



const Quill = (props) =>{
 
  const [content,setContent] = useState(props.msg);
  useEffect(() => {
    setContent(props.msg)  
  }, [props.msg])

  const btn = {
   marginTop: '50px',
   marginRight:'10px'
 };
 
  const modules = {
   toolbar: [
     [{ 'header': [ 2,3,4,5,6,false] }],
     [{ 'size': [] }],
     ['bold', 'italic', 'underline'],
     [{'list': 'ordered'}, {'list': 'bullet'}],
     [{ 'align': [] }],
   ],
 }

  return (
   <div>
   <ReactQuill  theme="snow" value={content} onChange={setContent} modules={modules} style = {{ height: '400px'}}/>    
   <Button type="primary" style={btn} onClick={()=>{props.changeValue(content);message.success("保存协议")}}>确定</Button>
   <Button style={btn} onClick={()=>{ setContent(''); props.changeValue('')}}>重置</Button>
   </div>
  ) 
}

const BasicInfo = () => { 
    const [, option] = useContext(ApplicationContext);
    const id = option.match!.params.id;
    const [fid, setFid] = useState("");
    const [logoUrl, setLogoUrl] = useState("");
    const [actionUrl, setActionUrl] = useState(uploadUri);
    const [backCont,setBackCont] = useState("");
    let res:any;
    console.log('res',res)

    return <ApolloSimpleFormStage
        id={id}
        query={queryAppletsConfigInfo}
        mutation={updateAppletsConfigInfo}
        dataPath={(data) => {   
             const websiteConfig = data.configInfo.appletsConfigInfo;                      
              setBackCont(websiteConfig.agreementContent)
            if (fid == "") {
                setLogoUrl(websiteConfig.logo);
            }
            if (id != null) {
                setActionUrl(uploadUri);
            }
            return {
                ...websiteConfig
            };
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    id,
                    logo: fid,
                    agreementContent: res ?? backCont
                }
            })
        }
        }
        >

       <div>
        <MerchantNameField required/>
        <SloganField required/>
        <DomainNameField required/>
        <NetworkSecurityField required/>
      
        <Form.Item label="小程序logo">
            <Upload name="file" action={actionUrl} onChange={(info) => {
                if (info.file.status == "done") {
                    const fid = info.file.response.data.archiveId;
                    setFid(fid);
                    setLogoUrl(info.file.response.data.url)
                }
            }}>
                <Button>选择文件</Button>
            </Upload>
            <img src={logoUrl} width={120} height={120}/>
            <div> （尺寸240*240px）</div>
        </Form.Item>
      </div> 
      <div>
          <p>用户协议：</p>
            <Quill msg={backCont} changeValue={(msg:string)=>{res = msg}}/>      
      </div>
      

    </ApolloSimpleFormStage>
};

export default () => {
    return <BasicInfo/>
}
