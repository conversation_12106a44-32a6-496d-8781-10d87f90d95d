import React, {useContext, useState,useEffect} from 'react';

import {Button, Form, Input, Upload,message} from 'antd';
import {ApolloSimpleFormStage, ApplicationContext, withForm} from '@lanmaoly/admin-framework';
import {queryWebsiteConfigInfo, updateWebsiteConfigInfo} from '../../../graphql';
import {SwitchField} from "../../../component/SwitchField";
import {enviroment} from "../../../env";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const uploadUri = enviroment.domain+"/api/archive/upload";

const MerchantNameField = withForm(Input, {
    label: "名称",
    name: ["merchantName"],
    rules: [{required: true, message: "用友"}]
});

const SloganField = withForm(Input, {
    label: "网站slogan",
    name: ["slogan"],
    rules: [{required: true, message: "请输入"}]
});

const DomainNameField = withForm(Input, {
    label: "网站域名",
    name: ["domainName"],
    rules: [{required: true, message: "请输入"}]
});

const ShowField = withForm(SwitchField, {
    label: "关闭官网入口",
    name: ["entranceEnable"],
});

const BottomDescriptionField = withForm(Input, {
    label: "网站底部其它说明",
    name: ["bottomDescription"],
    rules: [{required: true, message: "COPYRIGHT © 北京阿拉钉科技有限公司"}]
});

const ICPField = withForm(Input, {
    label: "网站备案号（ICP）",
    name: ["ICP"],
    rules: [{required: true, message: "京ICP备19024749号-1"}]
});

const AgreementName = withForm(Input, {
  label: "用户协议名称",
  name: ["agreementName"],
});

const NetworkSecurityField = withForm(Input, {
    label: "公网安备",
    name: ["networkSecurity"],
    rules: [{required: true, message: "https://beian.milt.gov.cn/#/Integrated/index"}]
});


const Quill = (props) =>{
 
   const [content,setContent] = useState(props.msg);
   useEffect(() => {
     setContent(props.msg)  
   }, [props.msg])

   const btn = {
    marginTop: '50px',
    marginRight:'10px'
  };

   const modules = {
    toolbar: [
      [{ 'header': [ 2,3,4,5,6,false] }],
      [{ 'size': [] }],
      ['bold', 'italic', 'underline'],
      [{'list': 'ordered'}, {'list': 'bullet'}],
      [{ 'align': [] }],
    ],
  }

   return (
    <div>
    <ReactQuill theme="snow" value={content} onChange={setContent} modules={modules} style = {{ height: '200px'}}/>    
    <Button type="primary" style={btn} onClick={()=>{props.changeValue(content);message.success("保存协议")}}>确定</Button>
    <Button style={btn} onClick={()=>{ setContent(''); props.changeValue('')}}>重置</Button>
    </div>
   ) 
}

const BasicInfo = () => {
    const [, option] = useContext(ApplicationContext);
    const id = option.match!.params.id;
    const [fid, setFid] = useState("");
    const [fid2, setFid2] = useState("");
    const [fid3, setFid3] = useState("");
    const [logoUrl, setLogoUrl] = useState("");
    const [tabLogoUrl, setTabLogoUrl] = useState("");
    const [appletQrCodeUrl, setAppletQrCodeUrl] = useState("");
    const [actionUrl, setActionUrl] = useState(uploadUri);
    const [backCont,setBackCont] = useState("");
    let res:any;
    return <ApolloSimpleFormStage
        id={id}
        query={queryWebsiteConfigInfo}
        mutation={updateWebsiteConfigInfo}
        dataPath={(data) => {
           const websiteConfig = data.configInfo.websiteConfigInfo;
              setBackCont(websiteConfig.agreementContent);
            if (fid == "") {
                setLogoUrl(websiteConfig.logo);
            }
            if (fid2 == "") {
                setTabLogoUrl(websiteConfig.tabLogo);
            }
            if (fid3 == "") {
                setAppletQrCodeUrl(websiteConfig.appletQrCode);
            }
            if (id != null) {
                setActionUrl(uploadUri);
            }
            return {            
                ...websiteConfig
            };             
                    
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    id,
                    logo: fid,
                    tabLogo: fid2,
                    appletQrCode: fid3,
                    entranceEnable: values.entranceEnable == null ? false : values.entranceEnable,
                    agreementContent: res ?? backCont
                }
            })
        }}>
        <MerchantNameField required/>
        <SloganField required/>
        <DomainNameField required/>
        <BottomDescriptionField required/>
        <ICPField required/>
        <NetworkSecurityField required/>
        <AgreementName required/>
       
        <ShowField/>

        <Form.Item label="网站logo">
            <Upload name="file" action={actionUrl} onChange={(info) => {
                if (info.file.status == "done") {
                    console.log(info.file.response);
                    const fid = info.file.response.data.archiveId;
                    setFid(fid);
                    setLogoUrl(info.file.response.data.url)
                }
            }}>
                <Button>选择文件</Button>
            </Upload>
            <img src={logoUrl} width={170} height={42}/>
            <text>  （尺寸170*42px）</text>
        </Form.Item>
        
        <Form.Item label="用户协议">
          
        <Quill msg={backCont} changeValue={(msg:string)=>{res = msg}}/>          
        </Form.Item>
         
        <Form.Item label="网站标签页logo">
            <Upload name="file" action={actionUrl} onChange={(info) => {
                if (info.file.status == "done") {
                    console.log(info.file.response);
                    const fid = info.file.response.data.archiveId;
                    setFid2(fid);
                    setTabLogoUrl(info.file.response.data.url)
                }
            }}>
                <Button>选择文件</Button>
            </Upload>
            <img src={tabLogoUrl} width={100} height={100}/>
            <text>  （尺寸100*100px）</text>
        </Form.Item>

        <Form.Item label="小程序二维码">
            <Upload name="file" action={actionUrl} onChange={(info) => {
                if (info.file.status == "done") {
                    console.log(info.file.response);
                    const fid = info.file.response.data.archiveId;
                    setFid3(fid);
                    setAppletQrCodeUrl(info.file.response.data.url)
                }
            }}>
                <Button>选择文件</Button>
            </Upload>
            <img src={appletQrCodeUrl} width={172} height={172}/>
            <text>  （尺寸172*172px）</text>
        </Form.Item>

    </ApolloSimpleFormStage>
};

export default () => {
    return <BasicInfo/>
}
