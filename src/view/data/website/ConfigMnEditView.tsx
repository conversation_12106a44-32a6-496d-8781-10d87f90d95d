import React, { useContext, useState } from 'react';

import { Form, Input, message, Radio } from 'antd';
import { ApolloSimpleFormStage, ApplicationContext, withForm } from '@lanmaoly/admin-framework';
import { queryMerchantInlineConfig, updateMerchantInlineConfig } from '../../../graphql';
import 'react-quill/dist/quill.snow.css';
import { enviroment } from "../../../env";

const BasicInfo = () => {
    const [isUpdate, setIsUpdate] = useState(true);
    const [text, setText] = useState('')
    const [tip, setTip] = useState('')
    const [fieldName, setFieldName] = useState('');
    const [remark, setRemark] = useState('');
    const [isShow, setIsShow] = useState(1);
    const [isRequired, setIsRequired] = useState(1);
    const [, option] = useContext(ApplicationContext);
    const id = option.match!.params.id;
    let res: any;

    return <ApolloSimpleFormStage
        id={id}
        query={queryMerchantInlineConfig}
        mutation={updateMerchantInlineConfig}
        dataPath={(data) => {
            const websiteConfig = data.configInfo.merchantInlineConfigDetail;
            if (isUpdate) {
                setIsShow(websiteConfig.showFlag)
                setIsRequired(websiteConfig.mustFlag)
                setFieldName(websiteConfig.fieldName)
                setRemark(websiteConfig.remark)
                if (websiteConfig.fieldName === '推荐码') {
                    setText('推荐码')
                    setTip('请输入推荐码')
                } else if (websiteConfig.fieldName === '所属银行') {
                    setText('所属银行')
                    setTip('请选择所属银行')
                }
                return {
                    ...websiteConfig
                };
            }
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    fieldName,
                    remark,
                    id: id ? id : '',
                    showFlag: isShow,
                    mustFlag: isShow ? isRequired : 0
                }
            })
        }
        }
    >

        <div>
            <Form.Item label="字段名称">
                <Input value={fieldName} placeholder={text} onChange={(val) => {
                    setIsUpdate(false)
                    setFieldName(val.target.value)
                }} />
            </Form.Item>
            <Form.Item label="提示语">
                <Input value={remark} placeholder={tip} onChange={(val) => {
                    setIsUpdate(false)
                    setRemark(val.target.value)
                }} />
            </Form.Item>
            <Form.Item label="是否显示">
                <Radio.Group
                    value={isShow}
                    onChange={(val) => {
                        setIsUpdate(false)
                        setIsShow(val.target.value)
                    }}>
                    <Radio value={1}>显示</Radio>
                    <Radio value={0}>隐藏</Radio>
                </Radio.Group>
            </Form.Item>
            {
                isShow == 1 ?
                    <Form.Item label="是否必填">
                        <Radio.Group
                            value={isRequired}
                            onChange={(val) => {
                                setIsUpdate(false)
                                setIsRequired(val.target.value)
                            }}>
                            <Radio value={1}>必填</Radio>
                            <Radio value={0}>选填</Radio>
                        </Radio.Group>
                    </Form.Item> : null
            }
        </div>


    </ApolloSimpleFormStage>
};

export default () => {
    return <BasicInfo />
}
