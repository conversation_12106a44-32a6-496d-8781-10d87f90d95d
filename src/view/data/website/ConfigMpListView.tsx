
import { Input, Button, message } from 'antd';
import React, { useContext,useState,useEffect } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import {apolloClient,queryAppletsConfigInfos} from '../../../graphql';

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const[disabled,setDisabled] = useState(false);

  const columns: DataTableColumnProps = [{
    title: '名称',
    dataIndex: 'name',
  }, {
    title: '创建日期',
    dataIndex: 'createTime',
  }]

  useEffect(function () {
    apolloClient.mutate({
        mutation: queryAppletsConfigInfos,
    }).then((res: any) => {
      const show = res.data.configInfo.appletsConfigInfos.data.length
      setDisabled(show)  
    })
}, [])

  const stage = useQueryStage({
    query: queryAppletsConfigInfos,
    columns,
    rowKey: "id",
    filters,
    // dataPath: (data) => data.configInfo.appletsConfigInfos
    dataPath: (data) => { 
      const websiteConfig = data.configInfo.appletsConfigInfos;   
      setDisabled(websiteConfig.data.length)     
      return {
        setDisabled,
          ...websiteConfig
      };          
   }
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/website/configMp/edit/${record.id}`)
    }
  }]
  const buttons = <Button.Group>
    <Button type="primary" disabled={disabled} onClick={() => {
      instance.history.push('/website/configMp/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}