
import { Input, Button, message } from 'antd';
import React, { useContext, useState, useEffect } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { apolloClient, queryMerchantInlineConfigList } from '../../../graphql';

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const [disabled, setDisabled] = useState(false);

  const columns: DataTableColumnProps = [
    {
      title: '字段名称',
      dataIndex: 'fieldName',
    },
    {
      title: '是否显示',
      dataIndex: 'showFlag',
      render: (v) => v == 1 ? "是" : "否"
    },
    {
      title: '是否必填',
      dataIndex: 'mustFlag',
      render: (v) => v == 1 ? "是" : "否"
    },
    {
      title: '提示语',
      dataIndex: 'remark',
    }
  ]

  useEffect(function () {
    apolloClient.mutate({
      mutation: queryMerchantInlineConfigList,
    }).then((res: any) => {
      const show = res.data.configInfo.merchantInlineConfigList.data.length
      // setDisabled(show)
    })
  }, [])

  const stage = useQueryStage({
    query: queryMerchantInlineConfigList,
    columns,
    rowKey: "id",
    filters,
    // dataPath: (res) => res.configInfo.merchantInlineConfigList.data
    dataPath: (data) => {
      const websiteConfig = data.configInfo.merchantInlineConfigList;
      setDisabled(websiteConfig.data.length)
      return {
        setDisabled,
        ...websiteConfig
      };
    }
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/website/configMn/edit/${record.id}`)
    }
  }]
  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/website/configMn/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}