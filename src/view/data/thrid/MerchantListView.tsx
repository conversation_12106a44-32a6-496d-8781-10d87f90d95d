import React,{useContext}   from 'react';
import { Input, Button} from 'antd';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, useQueryStage, DataTableOperation, FaIcon, ApplicationContext } from '@lanmaoly/admin-framework';
import { queryMerchantInfoList} from '../../../graphql';
const MerchantIdFilter = withForm(Input, {
  label: '商户ID',
  name: 'id',
})
const MerchantNameFilter = withForm(Input, {
  label: '商户名称',
  name: 'merchantName',
})
export default () => {
    const filters: QueryStageFilterOption[] = [{
      node: () => <MerchantIdFilter/>
    },{
      node: () => <MerchantNameFilter/>
    }]
    const [instance] = useContext(ApplicationContext)
    const columns: DataTableColumnProps = [{
      title: '商户ID',
      dataIndex: 'id',
      width: 80,
    }, {
      title: '商户名称',
      dataIndex: 'merchantName',
      width: 200,
    }, {
      title: '开通服务名称',
      dataIndex: 'openedProductName',
      width: 200,
      render:(value = []) => value.reduce((v, t, i) => v +  (i===0 ? '' : ',') +  t, '')
    }]
    const stage = useQueryStage({
      query: queryMerchantInfoList,
      columns,
      rowKey: "id",
      filters,
      dataPath: (data) => data.appstore.pageMerchantInfo,
      autoRefresh:true
    })
  const opers: DataTableOperation[] = [{
    title: '查看',
    icon: <FaIcon name="bars" />,
    action: (record) => {
      instance.history.push(`/thrid/merchant/detail/${record.id}`)
    }
  }];
    // const buttons = <Button.Group>
    // </Button.Group>
    return <div>
       <QueryStage
        instance={stage}
        operation={opers}
        // toolbar={buttons}
    />
    </div>
  }