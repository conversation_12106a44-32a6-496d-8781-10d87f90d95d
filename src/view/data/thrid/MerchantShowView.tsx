import React,{useContext}   from 'react';
import { Input, Button, Form, Card, Col, Row} from 'antd';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, useQueryStage, DataTableOperation, FaIcon, ApplicationContext , ApolloSimpleFormStage} from '@lanmaoly/admin-framework';
import { queryMerchantInfoListById} from '../../../graphql';

const ColumnItem = (...items) => {
  const [item] = items;
  let values = item?.value ?? "";
  values = Array.isArray(values) ? values.reduce((v,t, i) => v + (i==0 ? '' : ',') + t, '') : values;
  return <Form.Item>
        {values}
    </Form.Item>
}


const MerchantNameFormItem = withForm(ColumnItem, {
  label: "商户名称",
  name: ["merchantName"],
});

const MerchantIdFormItem = withForm(ColumnItem, {
  label: "商户id",
  name: ["id"],
});

const OpenedProductName = withForm(ColumnItem, {
  label: "已开通服务",
  name: ["openedProductName"],
});

const ApplyingProductName = withForm(ColumnItem, {
  label: "申请中服务",
  name: ["applyingProductName"],
});

const BasicInfo = () => { 
  const [, option] = useContext(ApplicationContext);
  const id = option.match!.params.id;
  return <ApolloSimpleFormStage
    id={id}
    query={queryMerchantInfoListById}
    dataPath={(data) => {
      const datas = data.appstore.merchantInfo
      return {
        ...datas
      }
    }}
  >
    <MerchantNameFormItem/>
    <MerchantIdFormItem/>
    <OpenedProductName name="已开通服务" />
    <ApplyingProductName name="申请中服务"/>
  </ApolloSimpleFormStage>;
}

export default () => <div>
  <BasicInfo/>
</div>