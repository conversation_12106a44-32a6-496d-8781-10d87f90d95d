
import { Input, Button, DatePicker, Form, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, useQueryStage, DataTableOperation, ApplicationContext } from '@lanmaoly/admin-framework';
import { useApolloClient } from "@apollo/react-hooks";
import { queryThridApplies, openApplyById, cancelApplyById, queryLayoutInfoList, queryProperties } from '../../../graphql';
import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
// import { useQuery } from "@apollo/react-hooks";

const { RangePicker } = DatePicker
const ProductNameFilter = withForm(Input, {
  label: '申请服务名称',
  name: 'productName',
})
const SupplierNameFilter = withForm(Input, {
  label: '服务供应商名称',
  name: 'supplierName',
})
const MerchantNameFilter = withForm(Input, {
  label: '申请企业名称',
  name: 'merchantName',
})
const MerchantIdFilter = withForm(Input, {
  label: '申请企业ID',
  name: 'merchantId',
})
const ProposerNameFilter = withForm(Input, {
  label: '申请人',
  name: 'proposerName',
})


const CustomDatePickerItem = (props) => {
  const { value, onChange } = props
  return <RangePicker showTime value={value || []} onChange={(values) => {
    if (onChange && values && values.length === 2) {
      var s = ""
      var e = ""
      if (values[0]) {
        s = values[0].format("YYYY-MM-DDThh:mm:ss")
      }
      if (values[1]) {
        e = values[1].format("YYYY-MM-DDThh:mm:ss")
      }
      onChange ? onChange([s, e]) : void (0)
    }
  }} />
}

export default () => {
  const [instance] = useContext(ApplicationContext)
  const client = useApolloClient();
  const filters: QueryStageFilterOption[] = [{
    node: () => <Form.Item name="applyTime" label="申请时间">
      <RangePicker showTime />
    </Form.Item>
  }, {
    node: () => <ProductNameFilter />
  }, {
    node: () => <SupplierNameFilter />
  }, {
    node: () => <MerchantNameFilter />
  }, {
    node: () => <MerchantIdFilter />
  }, {
    node: () => <ProposerNameFilter />
  }]

  const columns: DataTableColumnProps = [{
    title: '申请时间',
    dataIndex: 'applyTime',
    width: 200,
    render: function (v) {
      return v.replace("T", " ")
    }
  }, {
    title: '申请企业名称',
    dataIndex: 'merchantName',
    width: 200,
  }, {
    title: '申请企业ID',
    dataIndex: 'merchantId',
    width: 90
  }, {
    title: '申请服务名称',
    dataIndex: 'productName',
    width: 150
  }, {
    title: '服务供应商名称',
    dataIndex: 'supplierName',
  }, {
    title: '申请人',
    dataIndex: 'proposerName',
  }, {
    title: '申请人手机号',
    dataIndex: 'proposerMobile',
  }, {
    title: '申请人邮箱',
    dataIndex: 'proposerEmail',
  }, {
    title: '注册地/主营所在地',
    dataIndex: 'mainBusinessLocation',
  }, {
    title: '状态',
    dataIndex: 'status',
    render(val) {
      const obj = {
        APPLYING: "申请中",
        ALREADY_OPENED: "已开通",
        CANCELLED: "已取消",
        NOT_APPLIED: "未申请"
      };
      return obj[val];
    }
  }]
  const opers: DataTableOperation[] = [{
    title: '开通服务',
    icon: <CheckOutlined translate={""} />,
    action: (record) => {
      client.mutate({
        mutation: openApplyById,
        variables: {
          id: record.applyId
        }
      }).then(() => {
        message.success({
          content: "开通服务成功",
          duration: 0.5,
          onClose: () => window.location.reload()
        });
      });
    }
  }, {
    title: '取消申请',
    icon: <CloseOutlined translate={""} />,
    action: (record) => {
      client.mutate({
        mutation: cancelApplyById,
        variables: {
          id: record.applyId,
        }
      }).then(() => {
        message.success({
          content: "取消申请成功",
          duration: 0.5,
          onClose: () => window.location.reload()
        });
      });
    }
  }];
  const stage = useQueryStage({
    query: queryThridApplies,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.appstore.pageApplyInfo,
    autoRefresh: true
  })

  const buttons = <Button.Group>

  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      toolbar={buttons}
      operation={opers}
    />
  </div>
}
