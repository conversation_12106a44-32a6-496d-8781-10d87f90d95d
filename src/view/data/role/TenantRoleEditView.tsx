
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryRole, updateRole } from '../../../graphql';


const NameField = withForm(Input, {
  label: "名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入名称" }]
})
const AddressField = withForm(Input.TextArea, {
  label: "权限",
  name: ["access"],
})


const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryRole}
    mutation={updateRole}
    dataPath={(data) => {
      const role = data.authz.role
      return {
        ...role,
        access: role.access?.join("\n") ?? ""
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          access: values.access.trim().split("\n")
        }
      })
    }}>
    <NameField />
    <AddressField />
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}