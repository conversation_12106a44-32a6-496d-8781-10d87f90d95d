
import { But<PERSON>, DatePicker } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { querySendMailRecords } from '../../../graphql';

const TimeField = withForm(DatePicker.RangePicker, {
  label: '发送时间',
  name: 'time',
  rules: [{ required: true, message: "请输入内容" }]
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <TimeField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70
  }, {
    title: '邮件编码',
    dataIndex: 'mailCode',
    width: 100,
  }, {
    title: '模板名称',
    dataIndex: 'templateCode',
    width: 200
  }, {
    title: '收件人',
    dataIndex: 'to',
    width: 200
  }, {
    title: '发送结果',
    dataIndex: 'result',
    width: 100
  }, {
    title: '邮件参数',
    dataIndex: 'parameter',
    width: 300
  }, {
    title: '失败原因',
    dataIndex: 'errorInfo',
    width: 100
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
  }]

  const stage = useQueryStage({
    query: querySendMailRecords,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.mail.sendMailRecords,
    variables(variables) {
      let startTime, endTime
      if (variables.time) {
        startTime = variables.time[0].startOf('day')
        endTime = variables.time[1].startOf('day')
      }
      return {
        ...variables,
        startTime,
        endTime
      }
    }
  })


  const opers: DataTableOperation[] = []

  const buttons = <Button.Group>

  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}