
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryMailAccount, updateMailAccount } from '../../../graphql';
import { SwitchField } from "../../../component/SwitchField";



const CodeField = withForm(Input, {
  label: "编码",
  name: ["code"],
  rules: [{ required: true, message: "请输入内容" },
  { max: 30, message: "内容过长" }],
})

const DescribeField = withForm(Input, {
  label: "描述",
  name: ["describe"],
  rules: [{ required: true, message: "请输入内容" },
  { max: 50, message: "内容过长" }],
})

const ProtocolField = withForm(Input, {
  label: "协议",
  name: ["protocol"],
  initialValue: 'smtp',
  rules: [{ required: true, message: "请输入内容" },
  { max: 5, message: "内容过长" }],
})

const PortField = withForm(Input, {
  label: "端口",
  name: ["port"],
  initialValue: 465,
  rules: [{ required: true, message: "请输入内容" }],
})

const MailHostField = withForm(Input, {
  label: "邮件服务器",
  name: ["mailHost"],
  rules: [{ required: true, message: "请输入内容" },
  { max: 30, message: "内容过长" }],
})

const MailAddressField = withForm(Input, {
  label: "发件地址",
  name: ["mailAddress"],
  rules: [{ required: true, message: "请输入内容" },
  { max: 30, message: "内容过长" }],
})

const MailPasswordField = withForm(Input, {
  label: "密码",
  name: ["mailPassword"],
  rules: [{ required: true, message: "请输入内容" },
  { max: 30, message: "内容过长" }],
})

const MailNicknameField = withForm(Input, {
  label: "昵称",
  name: ["mailNickname"],
  rules: [{ required: true, message: "请输入内容" },
  { max: 20, message: "内容过长" }],
})

const IsAuthField = withForm(SwitchField, {
  label: "是否验证",
  name: ["isAuth"],
  initialValue: true
})

const EnabledDebugModField = withForm(SwitchField, {
  label: "调试",
  name: ["enabledDebugMod"],
})

const EnabledSslField = withForm(SwitchField, {
  label: "使用SSL",
  name: ["enabledSsl"],
  initialValue: true
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryMailAccount}
    mutation={updateMailAccount}
    dataPath={(data) => data.mail.mailAccount}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          isAuth: values.isAuth ?? false,
          enabledDebugMod: values.enabledDebugMod ?? false,
          enabledSsl: values.enabledSsl ?? false,
        }
      })
    }}>
    <CodeField />
    <DescribeField />
    <ProtocolField />
    <PortField />
    <MailHostField />
    <MailAddressField />
    <MailNicknameField />
    <MailPasswordField />
    <IsAuthField />
    <EnabledDebugModField />
    <EnabledSslField />
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
