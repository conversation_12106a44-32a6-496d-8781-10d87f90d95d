import {Button, Input, message, Modal} from 'antd';
import React, {useContext, useState} from 'react';
import {
    ApplicationContext,
    DataTableColumnProps,
    DataTableOperation,
    FaIcon,
    QueryStage,
    QueryStageFilterOption,
    useQueryStage,
    withForm
} from '@lanmaoly/admin-framework';
import {apolloClient, queryMailAccounts,checkSendMail} from '../../../graphql';
import {any} from "prop-types";


const KeywordsField = withForm(Input, {
    label: '关键字',
    name: 'keywords',
})


export default () => {

    const [email, setEmail] = useState("");
    const [record, setRecord] = useState({code:any});
    const [instance] = useContext(ApplicationContext)

    const [isModalVisible, setIsModalVisible] = useState(false);
    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleOk = () => {
        setIsModalVisible(false);
        apolloClient.mutate({
            mutation: checkSendMail,
            variables: {
                mailCode: record.code,
                to: email
            }
        }).then((reponse: any) => {
            if (reponse.data.mail.checkSendMail== "SUCCESS") {
                message.success("发送中，如需查看结果，请查看邮件发送记录")
            }else{
                message.warn("配置有误")
            }
        })
    };

    const handleCancel = () => {
        setIsModalVisible(false);
    };


    const filters: QueryStageFilterOption[] = [{
        node: () => <KeywordsField/>
    }]

    const columns: DataTableColumnProps = [{
        title: 'ID',
        dataIndex: 'id',
        width: 70
    }, {
        title: '编码',
        dataIndex: 'code',
        width: 200,
    }, {
        title: '描述',
        dataIndex: 'describe',
        width: 200
    }, {
        title: '邮件服务器',
        dataIndex: 'mailHost',
        width: 200
    }, {
        title: '发件地址',
        dataIndex: 'mailAddress',
    }, {
        title: '昵称',
        dataIndex: 'mailNickname',
    }, {
        title: '创建时间',
        dataIndex: 'createTime',
        sorter: true
    }]

    const stage = useQueryStage({
        query: queryMailAccounts,
        columns,
        rowKey: "id",
        filters,
        dataPath: (data) => data.mail.mailAccounts
    })


    const opers: DataTableOperation[] = [{
        title: '编辑',
        icon: <FaIcon name="pencil-square-o"/>,
        action: (record) => {
            instance.history.push(`/mail/account/edit/${record.id}`)
        }
    }, {
        title: '测试',
        icon: <FaIcon name="pencil-square-o"/>
        ,
        action: (record) => {
            setRecord(record)
            setIsModalVisible(true);
        }
    }]

    const buttons = <Button.Group>
        <Button type="primary" onClick={() => {
            instance.history.push('/mail/account/add')
        }}>新增</Button>
    </Button.Group>

    function handleChange(e) {

        const {value} = e.target
        setEmail(value)

    }

    return <div>
        <Modal
            title="请输入收件人地址"
            visible={isModalVisible}
            onOk={handleOk}
            onCancel={handleCancel}
        >
            <Input placeholder="邮箱地址" value={email} onChange={handleChange}/>
        </Modal>
        <QueryStage
            instance={stage}
            operation={opers}
            toolbar={buttons}
        />
    </div>
}