
import { Input, Button} from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryMailTemplates } from '../../../graphql';


const KeywordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeywordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70
  }, {
    title: '编码',
    dataIndex: 'code',
    width: 300,
  }, {
    title: '名称',
    dataIndex: 'name',
    width: 300
  }, {
      title: '创建时间',
      dataIndex: 'createTime',
      sorter: true
  }, {
    title: '内容',
    dataIndex: 'content',
    width: 500
  }]

  const stage = useQueryStage({
    query: queryMailTemplates,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.mail.mailTemplates
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/mail/template/edit/${record.id}`)
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/mail/template/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}