
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import {
  queryMailTemplate,
  updateMailTemplate
} from '../../../graphql';



const CodeField = withForm(Input, {
  label: "编码",
  name: ["code"],
  rules: [{ required: true, message: "请输入内容" }]
})

const NameField = withForm(Input, {
  label: "名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入内容" }]
})

const ContentField = withForm(Input.TextArea, {
  label: "内容",
  name: ["content"],
  rules: [{ required: true, message: "请输入内容" }]
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryMailTemplate}
    mutation={updateMailTemplate}
    dataPath={(data) => data.mail.mailTemplate}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
        }
      })
    }}>
    <CodeField readOnly={id} />
    <NameField />
    <ContentField rows={5} />
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
