
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryMerchant } from '../../../graphql';

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: '编号',
    dataIndex: 'id',
    width: 70,
    sorter: true
  }, {
    title: '商户名称',
    dataIndex: 'name',
    width: 200,
  }, {
    title: '邮箱',
    dataIndex: 'email',
    width: 90
  }, {
    title: '社会信用代码',
    dataIndex: 'unifiedCode',
    width: 150
  }, {
    title: '注册地址',
    dataIndex: 'registerAddress',
  }, {
    title: '开票地址',
    dataIndex: 'invoiceAddress',
  }, {
    title: '开票电话',
    dataIndex: 'invoiceMobile',
  }, {
    title: '存管用户编号',
    dataIndex: 'platformUserNo',
  }, {
    title: '法定代表人',
    dataIndex: 'legal',
  }, {
    title: '法定代表人证件号',
    dataIndex: 'legalIdCardNo',
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: queryMerchant,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.merchant.merchants
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      message.warn("暂时无法编辑")
    }
  }, {
    title: '删除',
    icon: <FaIcon name="delete-icon" />,
    confirmText: '确认要删除?',
    action: () => {
      message.warn("暂不允许删除")
    }
  }]

  const buttons = <Button.Group>
    
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}