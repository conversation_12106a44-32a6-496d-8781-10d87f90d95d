
import React, { useContext } from 'react';

import { Input, Checkbox } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryDeveloper, updateDeveloper } from '../../../graphql';


const IdField = withForm(Input, {
  label: "开发者编号",
  name: ["id"],
})
const PrivateKeyField = withForm(Input, {
  label: "私钥",
  name: ["privateKey"],
})
const CallbackUrlField = withForm(Input, {
  label: "回调地址",
  name: ["callbackUrl"],
})
const AllowAddressField = withForm(Input.TextArea, {
  label: "IP白名单",
  name: ["allowAddress"],
})
const FlagsField = withForm(Checkbox.Group, {
  label: "标志",
  name: ["flags"],
})

const options = [
  { label: '使用旧版回调方式', value: 'USE_OLD_CALLBACK' },
  { label: '使用本地数据库', value: 'USE_LEGACY_LOCAL_DATA' },
]; 



const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id
  const tenant = option.match!.params.tenant

  return <ApolloSimpleFormStage
    id={id}
    query={queryDeveloper}
    mutation={updateDeveloper}
    dataPath={(data) => {
      const developer = data.authz.developer
      return {
        ...developer,
        allowAddress: developer?.allowAddress.join("\n") ?? ""
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          tenant,
          allowAddress: values.allowAddress?.trim().split("\n") ?? ""
        }
      })
    }}>
    <IdField readOnly={id} placeholder="留空自动创建开发者编号" />
    <PrivateKeyField placeholder="留空自动创建开发者编号" />
    <CallbackUrlField />
    <AllowAddressField />
    <FlagsField options={options} />
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}