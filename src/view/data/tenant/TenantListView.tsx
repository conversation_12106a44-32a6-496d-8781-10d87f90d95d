
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryTenants } from '../../../graphql';

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  }, {
    title: '名称',
    dataIndex: 'name',
    width: 300,
  }, {
    title: '商户编号',
    dataIndex: 'merchantId',
    width: 90
  }, {
    title: '商户名称',
    dataIndex: ['merchant', 'name'],
  }, {
    title: '拥有角色',
    dataIndex: 'roles',
    render: (value) => value?.map(o => o.name).join(" , ")??""
  }]

  const stage = useQueryStage({
    query: queryTenants,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.authz.tenants
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/authz/tenant/edit/${record.id}`)
    }
  }, {
    title: '开发者账号',
    icon: <FaIcon name="ticket" />,
    action: (record) => {
      instance.history.push(`/authz/tenant/developer/${record.id}`)
    }
  }, {
    title: '删除',
    icon: <FaIcon name="delete-icon" />,
    confirmText: '确认要删除?',
    action: () => {
      message.warn("暂不允许删除")
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/authz/tenant/add')
    }}>新增</Button>
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}