
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryTenant, updateTenant } from '../../../graphql';
import { RoleSelect } from '../../../component/selects';


const NameField = withForm(Input, {
  label: "名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入名称" }]
})
const AddressField = withForm(Input, {
  label: "商户编号",
  name: ["merchantId"],
})
const RoleField = withForm(RoleSelect, {
  label: "角色",
  name: ["roles"],
})


const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryTenant}
    mutation={updateTenant}
    dataPath={(data) => data.authz.tenant}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          merchant: values.merchantId,
          roles: values.roles?.map(o => o.id) ?? []
        }
      })
    }}>
    <NameField />
    <AddressField />
    <RoleField mode="multiple" />
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}