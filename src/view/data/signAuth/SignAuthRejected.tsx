import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal, Form, Input, Button } from 'antd';

interface SignAuthRejectedProps {
  onConfirm: (id: number,reason: string) => void;
}

interface SignAuthRejectedRef {
  open: (number) => void;
}

const SignAuthRejected = forwardRef<SignAuthRejectedRef, SignAuthRejectedProps>(({ onConfirm }, ref) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [compId,setCompId] = useState<number>(0)
  useImperativeHandle(ref, () => ({
    open: (id: number) => {
      setVisible(true)
      setCompId(id)
      return 
    }
  }));

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };

  const handleConfirm = async () => {
    try {
      const values = await form.validateFields();
      onConfirm(compId,values.reason);
      setVisible(false);
      form.resetFields();
    } catch (error) {
      console.log('验证失败:', error);
    }
  };

  return (
    <Modal
      title="驳回"
      visible={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>取消</Button>,
        <Button key="confirm" type="primary" onClick={handleConfirm}>确定</Button>
      ]}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="reason"
          label="请填写驳回原因"
          rules={[{ max: 30, message: '驳回原因最多30字' }]}
        >
          <Input placeholder="请输入驳回原因（不多于30字）" />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default SignAuthRejected;