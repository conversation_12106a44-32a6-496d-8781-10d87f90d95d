import { Input, Button, message } from "antd";
import {
  CheckSquareOutlined,
  CloseSquareOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import React, { useContext, useRef } from "react";
import {
  QueryStage,
  QueryStageFilterOption,
  withForm,
  DataTableColumnProps,
  DataTableOperation,
  FaIcon,
  ApplicationContext,
  useQueryStage,
} from "@lanmaoly/admin-framework";
import SignAuthRejected from "./SignAuthRejected";
import { queryAuthorizationRecords,queryOutSideMerchants,updateOutSideMerchant,apolloClient } from "../../../graphql";
import { DatePicker } from "antd";
import CompInfo from "./CompInfo";
import { AuthStatus } from "./packageList";
const { RangePicker } = DatePicker;

const ResourceOwner = withForm(Input, {
  label: "",
  name: "keywords",
});

const DateField = withForm(RangePicker, {
  label: "时间范围",
  name: "createTime",
});

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {
  const [instance] = useContext(ApplicationContext);
  const rejectedRef = useRef<any>(null);
  const compInfoRef = useRef<any>(null);

  const filters: QueryStageFilterOption[] = [
    {
        node: () => <NameField />
    }
    // {
    //   node: () => <ResourceOwner />,
    // },
    // {
    //   node: () => <DateField allowClear showTime autoComplete="OFF" />,
    // },
  ];
  const columns: DataTableColumnProps = [
    { title: "企业名称", dataIndex: "merchantName", width: 120 },
    { title: "授权人姓名", dataIndex: "applyUserName", width: 100 },
    { title: "授权手机号", dataIndex: "applyUserCellPhone", width: 120 },
    {
      title: "审核状态", dataIndex: "applyStatus", width: 100, 
      render: (value) => AuthStatus[value]
    },
    { title: "签约时间", dataIndex: "signingDate", width: 150 },
    { title: "充值金额", dataIndex: "totalAmount", width: 100 },
    { title: "套餐数量", dataIndex: "planTotal", width: 100 },
    { title: "剩余数量", dataIndex: "planRemain", width: 100 },
  ];

  const stage = useQueryStage({
    query: queryOutSideMerchants,
    columns,
    rowKey: "merchantId",
    filters,
    dataPath: (data) => data.outSideMerchant.queryOutsideMerchant,
    autoRefresh:true
  });

  const listReload = () => {
    stage.refresh()
  }

  const opers: DataTableOperation[] = [
    {
      title: "通过",
      icon: <CheckSquareOutlined translate={0} />,
      action: async (record) => {

        if (record.applyStatus && record.applyStatus == 'PASS') {
          message.warning('该企业已通过审核,无需再次通过')
          return
        }
        const {errors} = await apolloClient.mutate({
          mutation: updateOutSideMerchant,
          variables: {
            id: record.merchantId,
            authStatus: 'PASS',
            remark: '',
          }
        })
        if (!errors.length) {
          listReload()
          message.success('授权成功')
        }
      },
    }, // 蓝色按钮
    {
      title: "拒绝",
      icon: <CloseSquareOutlined translate={0} />,
      action: (record) => {
        if (record.applyStatus && record.applyStatus == 'NOT_PASS') {
          message.warning('该企业已拒绝,无需再次拒绝')
          return
        }
        rejectedRef.current?.open(record.merchantId)
      },
    }, // 红色按钮
    {
      title: "详情",
      icon: <UnorderedListOutlined translate={0} />,
      action: async (record) => {
        const data = record; // 假设 getCompInfo 是一个异步函数，返回企业信息数据
        compInfoRef.current?.open(data);
        /* 详情跳转 */
      },
    },
  ];
  return (
    <div>
      <QueryStage instance={stage} operation={opers} />
      <SignAuthRejected
        ref={rejectedRef}
        onConfirm={async (id, reason) => {
          const {errors} = await apolloClient.mutate({
            mutation: updateOutSideMerchant,
            variables: {
              id: id,
              authStatus: 'NOT_PASS',
              remark: reason,
            }
          })

          if (!errors.length) {
            listReload()
            message.success('拒绝授权成功')
          }
        }}
      />
      <CompInfo ref={compInfoRef} onConfirm={function (reason: string): void {
        listReload()
        console.log(reason);
      }} />
      
    </div>
  );
};
