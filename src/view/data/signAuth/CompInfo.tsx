import React, {
  forwardRef,
  useImperative<PERSON><PERSON>le,
  useMemo,
  useState,
  useEffect,
} from "react";
import { Modal, Row, Col, Button, Table, Select, message } from "antd";
import { AuthStatus, packageOptions } from "./packageList";
import { queryMerchantPricePlan, apolloClient, addPricePlan } from "../../../graphql";
import { getToken } from "../../../utils";

interface SignAuthRejectedProps {
  onConfirm: (reason: string) => void;
}

interface SignAuthRejectedRef {
  open: (any) => void;
}

const CompInfo = forwardRef<SignAuthRejectedRef, SignAuthRejectedProps>(
  ({ onConfirm }, ref) => {
    const [visible, setVisible] = useState(false);
    // const [form] = Form.useForm();
    // const [compId, setCompId] = useState<number>(0);
    const [type, setType] = useState<string>("compInfo");
    const [compInfo, setCompInfo] = useState<any>({});
    const [selectedValue, setSelectedValue] = useState<string>('1')
    const title = useMemo(() => {
      if (type === "compInfo") {
        return "企业详情";
      } else if (type === "rechargeRecord") {
        return "充值记录";
      } else {
        // 充值显示企业名称rechargeRecord
        return compInfo.compName;
      }
    }, [type]);
    const modalWidth = useMemo(() => {
      if (type === "compInfo") {
        return 600; // 企业详情的宽度
      } else if (type === "rechargeRecord") {
        return 680; // 充值记录的宽度
      } else {
        return 400; // 充值的宽度
      }
    }, [type]);
    // 公司详情信息

    useImperativeHandle(ref, () => ({
      open: (compInfo) => {

        setCompInfo(compInfo);
        setType("compInfo");
        setVisible(true);
        // setCompId(id)
        return;
      },
    }));

    const handleCancel = () => {
      setVisible(false);
    };

    // 套餐充值
    const rechargeHandle = async () => {
      const option = packageOptions[Number(selectedValue) - 1]
      const {errors} = await apolloClient.mutate({
        mutation: addPricePlan,
        variables: {
          id: compInfo.merchantId,
          packageName: option.label,
          amount: option.total,
          packageNum: option.count,
          token: getToken()
        }
      })
      if (!errors.length) {
        onConfirm('充值成功')
        message.success('充值成功,请重新获取菜单')
      }
    }

    const handleConfirm = async () => {
      try {
        if (type == 'recharge') {
          Modal.confirm({
            title: '确认充值',
            content: '您确认要进行当前套餐的充值操作吗？',
            okText: '确认',
            okType: 'primary',
            cancelText: '取消',
            onOk: async () => {
              // 用户确认后执行提交逻辑
              await rechargeHandle();
              setVisible(false);
            },
            onCancel: () => {
              // 取消操作（可选，如提示）
              // message.info('已取消充值');
            },
          });
        }
        // setVisible(false);
      } catch (error) {
        console.log("验证失败:", error);
      }
    };

    const CompInfoBox = ({ type }) => {
      console.log("type", type);
      if (type != "compInfo") {
        return null;
      }
      return (
        <section>
          <h3>企业详情</h3>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={24}>企业名称：{compInfo.merchantName}</Col>
          </Row>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={12}>授权人姓名：{compInfo.applyUserName}</Col>
            <Col span={12}>授权人联系方式：{compInfo.applyUserCellPhone}</Col>
          </Row>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={12}>审核状态：{AuthStatus[compInfo.applyStatus]}</Col>
            <Col span={12}>审批完成日期：{compInfo.signingDate}</Col>
          </Row>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <h3>套餐详情</h3>
            <Button
              type="link"
              onClick={() => {
                setType("rechargeRecord");
              }}
            >
              充值记录
            </Button>
          </div>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={24}>套餐总量：{compInfo.planTotal}</Col>
          </Row>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={24}>当前套餐数量：{compInfo.planTotal - compInfo.planRemain}</Col>
          </Row>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={24}>剩余数量：{compInfo.planRemain}</Col>
          </Row>
          <Button
            type="primary"
            onClick={() => {
              setType("recharge");
            }}
          >
            充值
          </Button>
        </section>
      );
    };

    // 模拟充值记录数据
    const [rechargeRecords, setRechargeRecords] = useState<any[]>([]);
    const [pagination, setPagination] = useState({
      current: 1,
      pageSize: 5,
      total: 0,
    });

    // 模拟请求充值记录
    const fetchRechargeRecords = async (current: number, pageSize: number) => {
      const { data } = await apolloClient.query({
        query: queryMerchantPricePlan,
        variables: {
          id: compInfo.merchantId
        }
      })
      console.log(data)
      if(data.outSideMerchant.queryMerchantPricePlan){
        setRechargeRecords(data.outSideMerchant.queryMerchantPricePlan)
      }
      setPagination((prev) => ({ ...prev, total: 20 })); // 模拟总共有20条数据
    };

    // 类型切换时加载数据
    useEffect(() => {
      if (type === "rechargeRecord") {
        fetchRechargeRecords(pagination.current, pagination.pageSize);
      }
    }, [type]);

    const RechargeRecord = ({ type }) => {
      if (type != "rechargeRecord") {
        return null;
      }
      const columns = [
        { title: "序号", dataIndex: "no", key: "no", render: (text, record, index) => index + 1 },
        {
          title: "充值金额",
          dataIndex: "rechargeAmount",
          key: "rechargeAmount",
        },
        { title: "套餐", dataIndex: "planName", key: "planName" },
        {
          title: "充值时间", dataIndex: "rechargeTime", key: "rechargeTime",
          render: (text) => text.replace('T', ' ')
        },
        { title: "操作人", dataIndex: "operator", key: "operator" },
      ];

      return (
        <section>
          <Table
            rowKey={(record)=>record.rechargeTime}
            style={{height: '600px'}}
            columns={columns}
            dataSource={rechargeRecords}
            pagination={false}
          />
        </section>
      );
    };

    // 当前套餐详情
    const [currPackInfo, setCurrPackInfo] = useState<any>(packageOptions[0]);

    // 套餐切换
    const handlePackageChange = (value) => {
      setSelectedValue(value)
      setCurrPackInfo(packageOptions[value-1]);
    }
    // 充值
    const Recharge = ({ type }) => {
      if (type !== "recharge") {
        return null;
      }
      return (
        <section>
          <h3>套餐充值</h3>
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={24}>
              <Select value={selectedValue} style={{ width: "100%" }} options={packageOptions} onChange={handlePackageChange}>
              </Select>
            </Col>
          </Row>
          <h3>套餐详情</h3>
          <Row gutter={16}>
            <Col span={12}>套餐金额：{currPackInfo.total}</Col>
            <Col span={12}>套餐合同数量：{currPackInfo.count}</Col>
          </Row>
        </section>
      );
    };

    const ModelFotter = ({ type }) => {
      if (type == "compInfo") {
        return (
          <>
            <Button key="cancel" onClick={handleCancel}>
              取消
            </Button>
            <Button key="confirm" type="primary" onClick={handleCancel}>
              确定
            </Button>
          </>
        );
      }
      if (type == "rechargeRecord") {
        return (
          <>
            <Button key="cancel" onClick={() => {
              setType("compInfo");
            }}>
              确定
            </Button>
          </>
        );
      }
      if (type == "recharge") {
        return (
          <>
            <Button key="cancel" onClick={() => {
              setType("compInfo");
            }}>
              取消
            </Button>
            <Button key="confirm" type="primary" onClick={handleConfirm}>
              充值
            </Button>
          </>
        );
      }
    };

    return (
      <Modal
        width={modalWidth}
        title={title}
        visible={visible}
        onCancel={handleCancel}
        footer={[
          <ModelFotter type={ type} />
        ]}
      >
        <CompInfoBox type={type} />
        <RechargeRecord type={type} />
        <Recharge type={type} />
      </Modal>
    );
  }
);

export default CompInfo;
