
import { Input, Button, Modal } from 'antd';
import React, { useContext, useState } from 'react';
import {
  QueryStage,
  QueryStageFilterOption,
  withForm,
  DataTableColumnProps,
  DataTableOperation,
  FaIcon,
  ApplicationContext,
  useQueryStage,
  ApolloSimpleFormStage
} from '@lanmaoly/admin-framework';
import { queryBusinesses } from '../../../graphql';
const KeywordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeywordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 200
  }, {
    title: '业务编码',
    dataIndex: 'name',
    width: 200
  }, {
    title: '业务名称',
    dataIndex: 'remark',
    width: 200
  }, {
    title: '更新时间',
    dataIndex: ['createTime'],
  }]

  const stage = useQueryStage({
    query: queryBusinesses,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.merchantPrivileges.findBusinesses
  })


  const opers: DataTableOperation[] = [{
    title: '分配角色',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/roleprivilege/businessRole/edit/${record.id}`)
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/roleprivilege/businessRole/add')
    }}>新增业务</Button>
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}