import {Input, Button, message, Table} from 'antd';
import React, {useContext, useState} from 'react';
import {
    QueryStage,
    QueryStageFilterOption,
    withForm,
    DataTableColumnProps,
    DataTableOperation,
    FaIcon,
    ApplicationContext,
    useQueryStage,
    ApolloSimpleFormStage
} from '@lanmaoly/admin-framework';
import {
    queryPrivilegeList,
    getRole,
    updateRoleBindPrivileges,
    apolloClient,
    queryPrivilegeByRoleId
} from '../../../graphql';
import {DatePicker} from 'antd';
import {any} from "prop-types";

const {RangePicker} = DatePicker;


const RoleNameField = withForm(Input, {
    label: "角色名称",
    name: ["roleName"],
    rules: [{required: true, message: "请输入业务ID"}]
})

const IdField = withForm(Input, {
    label: "角色编码",
    name: ["id"],
    rules: [{required: true, message: "请输入业务ID"}]
})

const DescribeField = withForm(Input, {
    label: '描述',
    name: 'describe',
})

const NameField = withForm(Input, {
    label: '关键字',
    name: 'keywords',
})


export default () => {

    const [instance,] = useContext(ApplicationContext)
    const [, option] = useContext(ApplicationContext)
    const id = option.match!.params.id

    const filters: QueryStageFilterOption[] = []

    const columns: DataTableColumnProps = [{
        title: 'ID',
        dataIndex: 'id',
        width: 70
    }, {
        title: '权限名称',
        dataIndex: 'name',
        width: 200
    }, {
        title: '权限编码',
        dataIndex: 'code',
        width: 200,
    }, {
        title: '更新时间',
        dataIndex: ['createTime'],
    }]
    const elements = [any];
    const stage = useQueryStage({
        query: queryPrivilegeList,
        columns,
        rowKey: "id",
        filters,
        dataPath: (data) => data.merchantPrivileges.queryPrivileges,
        pageSize: 30000,
        autoRefresh: true,
        onDataChange: () => {
            apolloClient.mutate({
                mutation: queryPrivilegeByRoleId,
                variables: {
                    roleId: id
                }
            }).then((reponse: any) => {
                if (reponse.data.merchantPrivileges.queryPrivilegeByRoleId != null) {
                    let list = reponse.data.merchantPrivileges.queryPrivilegeByRoleId
                    list.forEach((item) => {
                        elements.push(item.id)
                    })
                }
                stage.setSelectedRowKeys(elements)
            })
        }

    })
    const buttons = <Button.Group>
        <Button></Button>
    </Button.Group>

    return <div>
        <ApolloSimpleFormStage
            id={id}
            query={getRole}
            mutation={updateRoleBindPrivileges}
            dataPath={(data) => {
                const smsChannel = data.merchantPrivileges.querySystemRole
                return {
                    ...smsChannel
                }

            }}
            onSubmit={(values, mutation) => {

                mutation({
                    variables: {
                        ...values,
                        roleId: id,
                        privilegeIds: stage.selectedRowKeys
                    }
                })
            }}>
            <IdField disabled={true} required/>
            <RoleNameField disabled={true}/>
            <DescribeField disabled={true}/>
        </ApolloSimpleFormStage>
        <QueryStage
            showQueryPanel={false}
            instance={stage}
            toolbar={buttons}
        />

    </div>
}