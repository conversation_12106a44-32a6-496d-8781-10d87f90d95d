
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { getMerchantRoles, merchantUserBindRole } from '../../../graphql';
import {SystemEnableRoleSelect} from "../../../component/selects";


const NameField = withForm(Input, {
    label: "商户管理员名称",
    name: ["name"],
    rules: [{ required: true, message: "请输入名称" }]
})
const RoleField = withForm(SystemEnableRoleSelect, {
    label: "更换角色",
    name: ["roles"]
})


const BasicInfo = () => {

    const [, option] = useContext(ApplicationContext)
    const id = option.match!.params.id
    const merchantId = option.match!.params.merchantId
    return <ApolloSimpleFormStage
        id={id}
        query={getMerchantRoles}
        mutation={merchantUserBindRole}
        dataPath={(data) => {
            return {
                roles: data.merchantPrivileges.getMerchantRoles
            }
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    merchantId:merchantId,
                    roleIds: values.roles?.map(o => o.id) ?? []
                }
            })
        }}>
        <RoleField  mode="multiple"/>
    </ApolloSimpleFormStage>
}

export default () => {

    return <BasicInfo />
}