
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryBusiness, businessBindRole } from '../../../graphql';
import {SystemEnableRoleSelect} from '../../../component/selects';


const NameField = withForm(Input, {
    label: "业务名称",
    name: ["remark"],
    rules: [{ required: true, message: "请输入名称" }]
})
const RoleField = withForm(SystemEnableRoleSelect, {
    label: "角色",
    name: ["roles"],
})


const BasicInfo = () => {

    const [, option] = useContext(ApplicationContext)
    const id = option.match!.params.id

    return <ApolloSimpleFormStage
        id={id}
        query={queryBusiness}
        mutation={businessBindRole}
        dataPath={(data) => data.merchantPrivileges.findBusiness}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    businessId:id,
                    roleIds: values.roles?.map(o => o.id) ?? []
                }
            })
        }}>
        <NameField />
        <RoleField mode="multiple" />
    </ApolloSimpleFormStage>
}

export default () => {

    return <BasicInfo />
}