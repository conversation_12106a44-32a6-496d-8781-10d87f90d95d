
import {Input, Button, Modal, message} from 'antd';
import React, { useContext, useState } from 'react';
import {
  QueryStage,
  QueryStageFilterOption,
  withForm,
  DataTableColumnProps,
  DataTableOperation,
  FaIcon,
  ApplicationContext,
  useQueryStage,
} from '@lanmaoly/admin-framework';
import {apolloClient, updateEnable, queryRolePrivilege} from '../../../graphql';
 
const KeywordsField = withForm(Input, {
  label: '角色名称',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)


  const NameField = withForm(Input, {
    label: "角色名称",
    name: ["name"],
    rules: [{ required: false, message: "请输入角色名称" }]
  })

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeywordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 60
  }, {
    title: '角色描述',
    dataIndex: 'roleName',
    width: 200
  }, {
    title: '角色编码',
    dataIndex: 'roleCode',
    width: 200
  }, {
    title: '角色状态',
    dataIndex: ['describe'],
    width: 200
  },{
    title: '排序号',
    dataIndex: 'seq',
    width: 50
  },  {
    title: '是否开启',
    dataIndex: ['enabled'],
    render: (v) =>  v == false ? "禁用" : "启用",
    width: 60
  }, {
      title: '更新时间',
      dataIndex: ['lastUpdateTime'],
    width: 180
  }]

  const stage = useQueryStage({
    query: queryRolePrivilege,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.merchantPrivileges.queryRole,
    variables(variables) {
      if (variables.option) {
        variables.option.sorts = [
          {
            field: "seq",
            direction: "asc"
          }
        ]
      }
      return {
        ...variables
      }
    }
  })


  const opers: DataTableOperation[] = [{
    title: '查看权限',
    icon: <FaIcon name="bars" />,
    action: (record) => {
      instance.history.push(`/roleprivilege/privilege/${record.id}`)
    }
  },{
    title: '编辑角色',
    icon: <FaIcon name="pencil-square-o"/>,
    action: (record) => {
      instance.history.push(`/roleprivilege/roleManager/update/${record.id}`)
    }
  },{
    title: '编辑权限',
    icon: <FaIcon name="bars"/>,
    action: (record) => {
      instance.history.push(`/roleprivilege/privilegeList/${record.id}`)
    }
  },{
    title: '启用/禁用',
    icon: <FaIcon name="pencil-square-o" />,
    confirmText: '你确定要更换状态吗?',
    action: (record) => {
        apolloClient.mutate({
          mutation: updateEnable,
          variables: {
            roleId: record.id,
            enable: !record.enabled
          }
        }).then((reponse: any) => {
          message.success("更改状态成功")
        })
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/roleprivilege/roleManager/add')
    }}>新增角色</Button>
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}