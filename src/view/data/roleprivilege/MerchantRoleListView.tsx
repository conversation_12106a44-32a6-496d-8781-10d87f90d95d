
import {Input, Button, Modal, message} from 'antd';
import React, { useContext, useState } from 'react';
import {
  QueryStage,
  QueryStageFilterOption,
  withForm,
  DataTableColumnProps,
  DataTableOperation,
  FaIcon,
  ApplicationContext,
  useQueryStage,
  ApolloSimpleFormStage
} from '@lanmaoly/admin-framework';
import { queryMerchant } from '../../../graphql';
 
const KeywordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeywordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 200
  }, {
    title: '商户名称',
    dataIndex: 'name',
    width: 200
  }, {
    title: '邮箱',
    dataIndex: 'email',
    width: 200
  }, {
    title: '更新时间',
    dataIndex: ['createTime'],
  }]

  const stage = useQueryStage({
    query: queryMerchant,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.merchant.merchants
  })

  const opers: DataTableOperation[] = [{
    title: '更换角色',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/roleprivilege/merchantRoleManager/add/${record.id}/${record.id}`)
      // if(record.merchantUser != null){
        
      // }else {
      //   message.warn("此商户没有操作员管理员")
      // }
    }
  }]

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
    />
  </div>
}