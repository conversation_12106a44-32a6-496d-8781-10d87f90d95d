import {observer} from 'mobx-react';
import {Col, DatePicker, Row, Table, Tree} from 'antd';
import {action, observable, computed, toJS} from "mobx";
import Column from 'antd/lib/table/Column';
import {DataTableColumnProps} from '@lanmaoly/admin-framework';
import {Input, Button, message} from 'antd';
import React, {useContext, useEffect, useState} from 'react';
import {QueryStage, QueryStageFilterOption, withForm, ApplicationContext} from '@lanmaoly/admin-framework';
import {apolloClient, queryPrivilegeGroup} from '../../../graphql';
import Explorer from "../../../component/Explorer";

const {RangePicker} = DatePicker;

const NameField = withForm(Input, {
    label: '关键字',
    name: 'keywords',
})

// export default () => {
export default observer(() => {
    const [instance,] = useContext(ApplicationContext)
    const [, option] = useContext(ApplicationContext)
    const id = option.match!.params.id
    const [treeDatas, settreeDatas] = useState([

    ])
    useEffect(function () {
        apolloClient.mutate({
            mutation: queryPrivilegeGroup,
            variables: {
                roleId: id,
            }
        }).then((reponse: any) => {
            settreeDatas(reponse.data.merchantPrivileges.getPrivilegeGroup)
        })
    },[])

    const filters: QueryStageFilterOption[] = [{
        node: () => <NameField/>
    }]
    return <div>
        {treeDatas.length != 0 &&
        <Explorer data={treeDatas} columns={[{
            title: '权限名称',
            dataIndex: 'name'
        },{
            title: '权限编码',
            dataIndex: 'code'
        }]} itemsLoader={async node => {
            console.log(node)
            return (node as any).privileges
        }}
                  childrenGetter={n => n.children ?? []}
                  titleGetter={n => n.businessCode == null?n.name:(n.name+"("+n.businessCode+")")}
                  keyGetter={n => n.id}
        />
        }

    </div>
})






