
import React, { useContext } from 'react';

import {Input, Table, InputNumber} from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { getRole, querySmsBusiness, updateRolePrivilege } from '../../../graphql';

const IdField = withForm(Input, {
  label: "角色名称",
  name: ["roleName"],
  rules: [{ required: true, message: "请输入业务ID" }]
})

const CodeField = withForm(Input, {
  label: "角色编码",
  name: ["roleCode"],
  rules: [{ required: true, message: "请输入业务ID" }]
})


const DescriptionField = withForm(Input, {
  label: "角色描述",
  name: ["describe"],
})

const SeqField = withForm(InputNumber, {
  label: "角色排序号",
  name: ["seq"],
  rules: [{required: true, message: "请输入排序号"}]
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
      id={id}
      query={getRole}
      mutation={updateRolePrivilege}
      dataPath={(data) => {
        const systemRole = data.merchantPrivileges.querySystemRole
        return {
          ...systemRole
        }

      }}
      onSubmit={(values, mutation) => {
        mutation({
          variables: {
            roleId : id,
            ...values,
          }
        })
      }}>
    <IdField required/>
    <CodeField disabled={id != null}/>
    <DescriptionField required/>
    <SeqField required/>
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
