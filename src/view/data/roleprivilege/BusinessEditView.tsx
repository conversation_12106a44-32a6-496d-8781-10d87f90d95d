
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import {querySmsBusiness, updateBusiness} from '../../../graphql';

const NameField = withForm(Input, {
    label: "业务编码",
    name: ["name"],
    rules: [{ required: true, message: "请输入业务ID" }]
})

const RemarkField = withForm(Input, {
    label: "业务名称",
    name: ["remark"],
    rules: [{ required: true, message: "请输入业务名称" }]
})


const BasicInfo = () => {

    const [, option] = useContext(ApplicationContext)
    const id = option.match!.params.id

    return <ApolloSimpleFormStage
        id={id}
        query={querySmsBusiness}
        mutation={updateBusiness}
        dataPath={(data) => {
            const smsChannel = data.sms.smsBusiness
            return {
                ...smsChannel
            }
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                }
            })
        }}>
        <NameField required/>
        <RemarkField required/>

    </ApolloSimpleFormStage>
}

export default () => {

    return <BasicInfo />
}
