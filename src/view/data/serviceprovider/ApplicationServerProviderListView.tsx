
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import {queryAgentInfos, queryApplicationServerProviders} from '../../../graphql';
 

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100
  }, {
    title: '租户ID',
    dataIndex: 'tenantId',
  }, {
    title: 'Logo',
    dataIndex: 'logoArchiveId',
  }, {
      title: '创建时间',
      dataIndex: 'createTime',
  }, {
      title: '修改时间',
      dataIndex: 'modifyTime',
  }]

  const stage = useQueryStage({
    query: queryApplicationServerProviders,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.authz.applicationServerProviders
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/authz/serviceProvider/edit/${record.id}`)
    }
  }]


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/authz/serviceProvider/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}