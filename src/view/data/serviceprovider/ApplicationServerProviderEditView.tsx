import React, {useContext, useState} from 'react';

import {Button, Form, Input, Upload} from 'antd';
import {ApolloSimpleFormStage, ApplicationContext, withForm} from '@lanmaoly/admin-framework';
import {queryApplicationServerProvider, updateApplicationServerProvider} from '../../../graphql';
import {enviroment} from "../../../env";

const uploadUri = enviroment.domain+"/api/archive/upload";


const SystemCodeField = withForm(Input, {
    label: "租户ID",
    name: ["tenantId"],
    rules: [{required: true, message: "请输入租户ID"}]
});


const BasicInfo = () => {

    const [, option] = useContext(ApplicationContext);
    const id = option.match!.params.id;
    const [fid, setFid] = useState("");
    const [imgUrl, setImgUrl] = useState("");
    const [actionUrl, setActionUrl] = useState(uploadUri);

    return <ApolloSimpleFormStage
        id={id}
        query={queryApplicationServerProvider}
        mutation={updateApplicationServerProvider}
        dataPath={(data) => {
            const smsConfig = data.authz.applicationServerProvider;
            setImgUrl(smsConfig.logoArchiveUrl);
            if (id != null) {
                setActionUrl(uploadUri);
            }
            return {
                ...smsConfig
            };
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    id,
                    logoArchiveId : fid,
                }
            })
        }}>
        <SystemCodeField required/>

        <Form.Item label="上传图片">
            <Upload name="file" action={actionUrl} onChange={(info) => {
                if (info.file.status == "done") {
                    console.log(info.file.response);
                    const fid = info.file.response.data.archiveId;
                    setFid(fid);
                    setImgUrl(info.file.response.data.url)
                }
            }}>
                <Button>选择文件</Button>
            </Upload>
            <img src={imgUrl} width={100} height={100}/>
        </Form.Item>

    </ApolloSimpleFormStage>
};

export default () => {
    return <BasicInfo/>
}
