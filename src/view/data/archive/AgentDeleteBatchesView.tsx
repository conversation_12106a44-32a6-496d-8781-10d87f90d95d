import React, {useContext, useState} from 'react';

import {<PERSON><PERSON>, Col, DatePicker, Form, Input, message, Popconfirm, Row} from 'antd';
import {withForm} from '@lanmaoly/admin-framework';
import {apolloClient, deleteArchiveBatches} from '../../../graphql';
import {FormInstance} from "antd/lib/form";

const {RangePicker} = DatePicker;


class BasicInfo extends React.Component<{
    navList?: any
}, {
    visible?: boolean,
    vaules?: any
}> {

    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            vaules: {}
        };

    }

    formRef:any = React.createRef<FormInstance>();

    render() {


        const layout = {
            labelCol: {span: 8},
            wrapperCol: {span: 16},
        };
        const tailLayout = {
            wrapperCol: {offset: 8, span: 16},
        };

        const showPopconfirm = () => {
            this.formRef.current.validateFields().then(()=>{
                this.setState({
                    visible: true
                });
            })
        };
        const handleOk = () => {
            apolloClient.mutate({
                mutation: deleteArchiveBatches,
                variables: {
                    expired: this.formRef.current.getFieldsValue().expired
                }
            }).then((reponse: any) => {
                if (reponse.errors.length == 0) {
                    message.success("异步删除中，请等待！")
                }
                this.setState({
                    visible: false
                });
            })
        };
        const handleCancel = () => {
            console.log('Clicked cancel button');

            this.setState({
                visible: false
            });
        };

        return (
            <Form
                {...layout}
                ref={this.formRef}
                name="basic"
                initialValues={{remember: true}}
            >
                <Row>
                    <Col span={20}>
                        <Form.Item
                            label="过期时间范围"
                            name="expired"
                            rules={[{required: true, message: '请选择过期时间'}]}
                        >
                            <RangePicker allowClear showTime autoComplete="OFF"/>
                        </Form.Item>
                    </Col>
                </Row>
                <Form.Item {...tailLayout}>
                    <Popconfirm
                        title="确认删除吗"
                        visible={this.state.visible}
                        onConfirm={handleOk}
                        onCancel={handleCancel}
                    >
                        <Button type="primary" onClick={showPopconfirm}>
                            删除
                        </Button>
                    </Popconfirm>

                    <Button
                        style={{margin: '0 8px'}}
                        onClick={() => {
                            this.formRef.current.resetFields();
                        }}
                    >
                        重置
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}

export default () => {

    return <BasicInfo/>
}