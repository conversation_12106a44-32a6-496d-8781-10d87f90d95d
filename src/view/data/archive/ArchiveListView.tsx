
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import {apolloClient, deleteArchive, queryArchives} from '../../../graphql';

import { DatePicker } from 'antd';
import {get} from "http";
const { RangePicker} = DatePicker;

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
});
const DateField = withForm(RangePicker, {
  label: '上传时间范围',
  name: 'uploadTime',
});
const DateField2 = withForm(RangePicker, {
  label: '过期时间范围',
  name: 'expired',
});
    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />,
  },{
    node: () => <DateField allowClear showTime autoComplete="OFF"/>
  },{
    node: () => <DateField2 allowClear showTime autoComplete="OFF"/>
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100
  }, {
    title: '名称',
    dataIndex: 'name',
  }, {
    title: '备注',
    dataIndex: 'remark',
  }, {
    title: '字节大小',
    dataIndex: 'size',
  }, {
    title: '上传时间',
    dataIndex: 'uploadTime',
    sorter: true
  }, {
    title: '过期时间',
    dataIndex: 'expired',
    sorter: true
  }, {
    title: '上传状态',
    dataIndex: 'uploadStatus',
  }]

  const stage = useQueryStage({
    query: queryArchives,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.archiveInfo.archiveInfos
  })


  const opers: DataTableOperation[] = [{
    title: '下载',
    icon: <FaIcon name="download-data" />,
    action: (record) => {
      document.location.href = "/api/platform-boss/api/download/"+`${record.id}` + "/" + `${record.name}`
    }
  }, {
    title: '删除',
    icon: <FaIcon name="delete-icon" />,
    confirmText: '确认要删除?',
    action: (record) => {
      apolloClient.mutate({
        mutation:deleteArchive,
        variables: {
          id: `${record.id}`
        }
      }).then((reponse:any) => {
        if(reponse.errors.length == 0){
          message.success("异步删除中！")
        }
      })
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/archive/info/deleteBatches')
    }}>批量删除</Button>
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}