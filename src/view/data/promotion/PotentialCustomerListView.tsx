
import { Input, Button, message, Select} from 'antd'; 
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { potentialCustomerQuery } from '../../../graphql';
import { IsLinkedUpFieldSelect } from '../../../component/selects';

 
import { DatePicker } from 'antd';
const { RangePicker} = DatePicker;


const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})


const ProductField = withForm(Input, {
  label: '产品',
  name: 'product',
})
 
 
const DateField = withForm(RangePicker, {
  label: '时间范围',
  name: 'createTime',
  
})


const IsLinkedUpField = withForm(IsLinkedUpFieldSelect, {
  label: "是否沟通",
  name: "isLinkedUp"
  
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  },{
    node: () => <ProductField />
  },{
    node: () => <IsLinkedUpField />
  },{
    node: () => <DateField allowClear showTime autoComplete="OFF"/>
  }]



  

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  }, {
    title: '公司名称',
    dataIndex: 'companyName' 
  }, {
    title: '联系人姓名',
    dataIndex: 'contactName' 
  }, {
    title: '手机号码',
    dataIndex: 'contactMobile',
  }, {
    title: '产品',
    dataIndex: 'potentialProduct',
  },{
    title: '提交时间',
    dataIndex: 'createTime',   
    sorter: true
  },{
    title: '是否沟通',
    dataIndex: 'isLinkedUp', 
    render: (v) =>  v == true ? "是" : "否"
  }]

  const stage = useQueryStage({
    query: potentialCustomerQuery,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.promotion.potentialCustomers
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/potential/customer/edit/${record.id}`)
    }
  }]
 

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
 
    />
  </div>
}