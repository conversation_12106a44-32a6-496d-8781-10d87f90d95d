
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryPotentialCustomer, updatePotentialCustomer } from '../../../graphql';
import { SwitchField } from '../../../component/SwitchField';


const CompanyNameField = withForm(Input, {
  label: "公司名称",
  name: ["companyName"] 
})
const ContactNameField = withForm(Input, {
  label: "联系人姓名",
  name: ["contactName"],
})
const ContactMobileField = withForm(Input, {
  label: "手机号",
  name: ["contactMobile"]
})

const PotentialProductField = withForm(Input, {
  label: "产品",
  name: ["potentialProduct"]
})


const CreateTimeField = withForm(Input, {
  label: "提交时间",
  name: ["createTime"]
})

const IsLinkedUpField = withForm(<PERSON><PERSON>Field, {
  label: "是否沟通",
  name: ["isLinkedUp"]
})

const RemarkField = withForm(Input.TextArea, {
  label: "备注",
  name: ["remark"],
  rules: [{ required: true, message: "必填,不能超过200字" }]
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryPotentialCustomer}
    mutation={updatePotentialCustomer}
    dataPath={(data) => {
      const potentialCustomer = data.promotion.potentialCustomer
      return {
        ...potentialCustomer 
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          isLinkedUp: values.isLinkedUp == '' ? false : true
        }
      })
    }}>
    <CompanyNameField readOnly/>
    <ContactNameField readOnly/>
    <ContactMobileField readOnly/>
    <PotentialProductField readOnly/>
    <CreateTimeField readOnly/>
    <IsLinkedUpField/>
    <RemarkField/>
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}