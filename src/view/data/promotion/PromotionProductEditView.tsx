
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryPromotionProduct, updatePromotionProduct } from '../../../graphql';
import { SwitchField } from '../../../component/SwitchField';


const ChannelTypeField = withForm(Input, {
  label: "渠道",
  name: ["channelType"],
  rules: [{ required: true, message: "请输入渠道名称" }]
})
const ProductField = withForm(Input.TextArea, {
  label: "产品",
  name: ["products"],
})
const NitifyMobileField = withForm(Input.TextArea, {
  label: "通知手机号",
  name: ["notifyMobile"],
  rules: [{ required: true, message: "多个手机号之间用逗号隔开" }]
})

const StatusField = withForm(SwitchField, {
  label: "状态",
  name: ["status"]
})


const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryPromotionProduct}
    mutation={updatePromotionProduct}
    dataPath={(data) => {
      const promotionProduct = data.promotion.promotionProduct
      return {
        ...promotionProduct,
        status: promotionProduct.status == 'CLOSED'? "" : promotionProduct.status
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          status: values.status == '' ? "CLOSED" : "OPEN"
        }
      })
    }}>
    <ChannelTypeField readOnly/>
    <ProductField />
    <NitifyMobileField />
    <StatusField />

  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}