
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { promotionProductQuery } from '../../../graphql';

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  }, {
    title: '渠道',
    dataIndex: 'channelType',
    width: 100,
    render: (v) =>  v == "PC" ? "PC官网" : "手机端"
  }, {
    title: '产品名称',
    dataIndex: 'products',
    width: 400
  }, {
    title: '状态',
    dataIndex: 'status',
    render: (v) =>  v == "OPEN" ? "开启" : "关闭"
  }, {
    title: '更新时间',
    dataIndex: 'updateTime'
  }]

  const stage = useQueryStage({
    query: promotionProductQuery,
    columns,
    rowKey: "id",
    filters,
    // dataPath: (data) => data.promotion.promotionProducts,
    dataPath: (data) => {
      const promotionProduct = data.promotion.promotionProducts
      return {
        ...promotionProduct,
        status: promotionProduct.status == 'CLOSED' ? "关闭" : "打开"
      }
    }
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/promotion/product/edit/${record.id}`)
    }
  }]

 

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
 
    />
  </div>
}