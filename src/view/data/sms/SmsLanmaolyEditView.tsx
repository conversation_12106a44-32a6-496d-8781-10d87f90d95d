
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { querySmsLanmaolyConfig, updateSmsLanmaolyConfig } from '../../../graphql';
 


const NameField = withForm(Input, {
  label: "名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入名称" }]
})

const SystemNameField = withForm(Input, {
  label: "通道系统名称",
  name: ["systemName"],
  rules: [{ required: true, message: "请输入通道系统名称" }]
})

const BizNameField = withForm(Input, {
  label: "业务名称",
  name: ["bizName"],
  rules: [{ required: true, message: "请输入业务名称" }]
})
const PrivateKeyField = withForm(Input, {
    label: "私钥",
    name: ["privateKey"],
    rules: [{ required: true, message: "请输入私钥" }]
})
const SignField = withForm(Input, {
    label: "签名",
    name: ["sign"],
    rules: [{ required: true, message: "请输入签名" }]
})



const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={querySmsLanmaolyConfig}
    mutation={updateSmsLanmaolyConfig}
    dataPath={(data) => {
      const smsLanmaolyConfig = data.sms.smsLanmaolyConfig
      return {
        ...smsLanmaolyConfig
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
        }
      })
    }}>
    <NameField required/>
    <SystemNameField required />
    <PrivateKeyField required />
    <BizNameField required />
    <SignField required />

  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
