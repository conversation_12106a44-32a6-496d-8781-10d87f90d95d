
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { querySmsChannel, updateSmsChannel } from '../../../graphql';
 


const NameCodeField = withForm(Input, {
  label: "通道名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入系统编码" }]
})

const ProviderField = withForm(Input, {
  label: "通道服务商",
  name: ["provider"],
  rules: [{ required: true, message: "请输入系统名称" }]
})

const PrivateKeyField = withForm(Input, {
  label: "接口加密的私钥",
  name: ["privateKey"],
})



const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={querySmsChannel}
    mutation={updateSmsChannel}
    dataPath={(data) => {
      const smsChannel = data.sms.smsChannel
      return {
        ...smsChannel
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
        }
      })
    }}>
    <NameCodeField required/>
    <ProviderField />
    <PrivateKeyField required />

  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
