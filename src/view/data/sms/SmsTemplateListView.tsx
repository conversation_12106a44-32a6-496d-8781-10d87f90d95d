
import { Input, Button } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { querySmsTemplates } from '../../../graphql';
// import {ExportExcel} from "../../../exportExcel";
 
const KeywordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeywordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 40 
  }, {
    title: '名称',
    dataIndex: 'name',
    width: 300,
  }, {
    title: '模板内容',
    dataIndex: 'template'
  }, {
      title: '描述',
      dataIndex: 'description'
  }]

  const stage = useQueryStage({
    query: querySmsTemplates,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.sms.smsTemplates
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/sms/template/edit/${record.id}`)
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/sms/template/add')
    }}>新增</Button>

    {/*<ExportExcel*/}
    {/*    query={stage.options.query}*/}
    {/*    variables={()=>stage.lastActualVariables}*/}
    {/*    columns={stage.columns}*/}
    {/*    dataRoot={"sms.smsTemplates"}*/}
    {/*    fileName={"短信模板"}/>*/}
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}