
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { querySmsConfigs } from '../../../graphql';
 

const SystemCodeField = withForm(Input, {
  label: '系统编码',
  name: 'systemCode',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <SystemCodeField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '系统编码',
    dataIndex: 'systemCode',
    width: 200,
  }, {
    title: '系统名称',
    dataIndex: 'systemName',
    width: 200
  }, {
    title: '短信系统编码',
    dataIndex: 'smsCode',
    width: 200
  }, {
    title: '私钥',
    dataIndex: 'privateKey',
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: querySmsConfigs,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.sms.smsConfigs
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/sms/config/edit/${record.id}`)
    }
  }]


 


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/sms/config/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}