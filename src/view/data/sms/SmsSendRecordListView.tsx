
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { smsSendRecords } from '../../../graphql';
import { DatePicker } from 'antd'; 
import { format } from 'path';
const { RangePicker} = DatePicker;

const MobileField = withForm(Input, {
  label: '手机号码',
  name: 'mobile',
})


const SystemCodeField = withForm(Input, {
  label: '系统编码',
  name: 'systemCode',
})
 
const BusinessCodeField = withForm(Input, {
  label: '业务编码',
  name: 'businessCode',
}) 
   
const StartTimeField = withForm(DatePicker, {
  label: '开始时间',
  name: 'startTime'
})


const EndTimeField = withForm(DatePicker,  {
  label: '截止时间',
  name: 'endTime'
})
 

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <MobileField />
  },{
    node: () => <SystemCodeField />
  },{
    node: () => <BusinessCodeField />
  },{
    node: () => <StartTimeField allowClear showTime autoComplete="OFF" />
  },{
    node: () => <EndTimeField allowClear showTime autoComplete="OFF" />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '手机号码',
    dataIndex: 'mobile',
    width: 100,
  },{
    title: '系统编码',
    dataIndex: 'systemCode',
    width: 100,
  },{
    title: '业务编码',
    dataIndex: 'businessCode',
    width: 100,
  }, {
    title: '请求参数',
    dataIndex: 'requestParams'
  }, {
    title: '状态',
    dataIndex: 'status',
    width: 100
  },{
    title: '备注',
    dataIndex: 'remark',
    width: 100
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  },  {
    title: '更新时间',
    dataIndex: 'updateTime'
  }]

  const stage = useQueryStage({
    query: smsSendRecords,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.sms.smsSendRecords
  })
 

  const buttons = <Button.Group>
    
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
 
      toolbar={buttons}
    />
  </div>
}