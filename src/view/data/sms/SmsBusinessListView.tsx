
import { Input, Button } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { querySmsBusinesses } from '../../../graphql';
 
const KeywordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeywordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: '业务编码',
    dataIndex: 'id',
    width: 200
  }, {
    title: '签名',
    dataIndex: 'sign',
    width: 200
  }, {
    title: '模板名称',
    dataIndex: ['template', 'name'],
    width: 200
  }, {
    title: '模板ID',
    dataIndex: ['template', 'id']
  }, {
      title: '通道ID',
      dataIndex: ['channel', 'id']
  }, {
      title: '通道名称',
      dataIndex: ['channel', 'name']
  }, {
      title: '描述',
      dataIndex: 'description',
      width: 200,
  }]

  const stage = useQueryStage({
    query: querySmsBusinesses,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.sms.smsBusinesses
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/sms/business/edit/${record.id}`)
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/sms/business/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}