
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { querySmsLanmaolyConfigs } from '../../../graphql';


const NameField = withForm(Input, {
  label: '通道名称',
  name: 'name',
})
const SystemNameField = withForm(Input, {
    label: '通道系统名称',
    name: 'systemName',
})
const BizNameField = withForm(Input, {
    label: '业务名称',
    name: 'bizName',
})


export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  },{
      node: () => <SystemNameField />
  },{
      node: () => <BizNameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '名称',
    dataIndex: 'name',
    width: 200,
  }, {
    title: '通道系统名称',
    dataIndex: 'systemName',
    width: 200
  }, {
    title: '业务名称',
    dataIndex: 'bizName',
    width: 200
  }, {
      title: '私钥',
      dataIndex: 'privateKey',
      width: 200
  }, {
      title: '签名',
      dataIndex: 'sign',
      width: 200
  }]

  const stage = useQueryStage({
    query: querySmsLanmaolyConfigs,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.sms.smsLanmaolyConfigs
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/sms/lanmaolyconfig/edit/${record.id}`)
    }
  }]


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/sms/lanmaolyconfig/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}