
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { querySmsChannels } from '../../../graphql';


const NameField = withForm(Input, {
  label: '通道名称',
  name: 'name',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '通道名称',
    dataIndex: 'name',
    width: 200,
  }, {
    title: '通道服务商',
    dataIndex: 'provider',
    width: 200
  }, {
    title: '接口加密的私钥',
    dataIndex: 'privateKey',
    width: 200
  }]

  const stage = useQueryStage({
    query: querySmsChannels,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.sms.smsChannels
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/sms/channel/edit/${record.id}`)
    }
  }]


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/sms/channel/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}