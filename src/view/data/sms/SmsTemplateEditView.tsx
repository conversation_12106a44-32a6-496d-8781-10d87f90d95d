
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { querySmsTemplate, updateSmsTemplate } from '../../../graphql';
 
const NameField = withForm(Input, {
  label: "模板名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入模板名称" }]
})

const DescriptionField = withForm(Input, {
  label: "模板描述",
  name: ["description"],
})

const TemplateField = withForm(Input.TextArea, {
  label: "模板内容",
  name: ["template"],
  rules: [{ required: true, message: "请输入模板内容" }]
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={querySmsTemplate}
    mutation={updateSmsTemplate}
    dataPath={(data) => {
      const smsConfig = data.sms.smsTemplate
      return {
        ...smsConfig
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
        }
      })
    }}>
    <NameField required/>
    <DescriptionField />
    <TemplateField required />

  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
