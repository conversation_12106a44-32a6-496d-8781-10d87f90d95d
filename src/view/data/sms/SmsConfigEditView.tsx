
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { querySmsConfig, updateSmsConfig } from '../../../graphql';
 


const SystemCodeField = withForm(Input, {
  label: "系统编码",
  name: ["systemCode"],
  rules: [{ required: true, message: "请输入系统编码" }]
})

const SystemNameField = withForm(Input, {
  label: "系统名称",
  name: ["systemName"],
  rules: [{ required: true, message: "请输入系统名称" }]
})

const SmsCodeField = withForm(Input, {
  label: "短信系统编码",
  name: ["smsCode"],
  rules: [{ required: true, message: "请输入短信系统编码" }]
})

const PrivateKeyField = withForm(Input.TextArea, {
  label: "私钥",
  name: ["privateKey"],
  rules: [{ required: true, message: "请输入私钥" }]
}) 


const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={querySmsConfig}
    mutation={updateSmsConfig}
    dataPath={(data) => {
      const smsConfig = data.sms.smsConfig
      return {
        ...smsConfig
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          status: values.status == '' ? "CLOSED" : "OPEN"
        }
      })
    }}>
    <SystemCodeField required/>
    <SystemNameField />
    <SmsCodeField required />
    <PrivateKeyField required />

  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
