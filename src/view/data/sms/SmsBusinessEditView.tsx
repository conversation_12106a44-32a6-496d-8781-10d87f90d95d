
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { querySmsBusiness, updateSmsbusiness } from '../../../graphql';
import {ChannelSelect,TemplateSelect} from "../../../component/selects";

const IdField = withForm(Input, {
    label: "业务ID",
    name: ["id"],
    rules: [{ required: true, message: "请输入业务ID" }]
})

const DescriptionField = withForm(Input, {
    label: "描述",
    name: ["description"],
})


const TemplateIdField = withForm(TemplateSelect, {
    label: "模板名称",
    name: ['template'],
    rules: [{ required: true, message: "请选择模板内容" }]
})

const SignField = withForm(Input, {
    label: "签名",
    name: ["sign"],
})

const ChannelIdField = withForm(ChannelSelect, {
    label: "通道名称",
    name: ['channel'],
    rules: [{ required: true, message: "请输入通道内容" }]
})


const BasicInfo = () => {

    const [, option] = useContext(ApplicationContext)
    const id = option.match!.params.id

    return <ApolloSimpleFormStage
        id={id}
        query={querySmsBusiness}
        mutation={updateSmsbusiness}
        dataPath={(data) => {
            const smsChannel = data.sms.smsBusiness
            return {
                ...smsChannel
            }
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    templateId:values.template.id,
                    channelId:values.channel.id
                }
            })
        }}>
        <IdField required/>
        <DescriptionField required/>
        <TemplateIdField />
        <SignField  />
        <ChannelIdField />

    </ApolloSimpleFormStage>
}

export default () => {

    return <BasicInfo />
}
