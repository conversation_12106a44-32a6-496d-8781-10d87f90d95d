
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryAuthorizationScope, updateAuthorizationScope } from '../../../graphql';
 


const NameField = withForm(Input, {
  label: "授权项名称",
  name: ["name"],
  rules: [{ required: true, message: "请输入授权项名称" }]
})
 

const DescriptionField = withForm(Input.TextArea, {
  label: "描述",
  name: ["description"],
  rules: [{ required: false, message: "请输入描述信息" }]
}) 


const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryAuthorizationScope}
    mutation={updateAuthorizationScope}
    dataPath={(data) => {
      const authorizationScopeConfig = data.authz.authorizationScope
      return {
        ...authorizationScopeConfig
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          status: values.status == '' ? "CLOSED" : "OPEN"
        }
      })
    }}>
    <NameField required/> 
    <DescriptionField required />

  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
