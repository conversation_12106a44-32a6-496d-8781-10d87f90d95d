
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryAuthorizationScopes } from '../../../graphql';
 

const KeyWordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeyWordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '授权项名称',
    dataIndex: 'name',
    width: 200,
  }, {
    title: '描述',
    dataIndex: 'description',
    width: 200
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: queryAuthorizationScopes,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.authz.authorizationScopes
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/authz/authorizationscope/edit/${record.id}`)
    }
  }]


 


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/authz/authorizationscope/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}