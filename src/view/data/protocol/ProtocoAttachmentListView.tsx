
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryProtocolAttachment } from '../../../graphql';
import { enviroment } from '../../../env';
import { DatePicker } from 'antd';
 

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})
 
export default () => {

  const [instance,] = useContext(ApplicationContext)

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  },{
    title: '附件ID',
    dataIndex: 'archiveId',
  },{
    title: '时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  
  const stage = useQueryStage({
    query: queryProtocolAttachment,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.protocol.queryProtocolAttachment,
    variables: {
      protocolId: id
    }
  })


  const opers: DataTableOperation[] = [ {
    title: '下载',
    icon: <FaIcon name="download-data" />,
    action: (record) => { 
      document.location.href = "/api/platform-boss/api/download/"+`${record.archiveId}` + "/" + `${record.archiveId}` + ".pdf"
    }
  }]
  

  const buttons = <Button.Group>
    
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage} 
      operation={opers}
      toolbar={buttons}
    />
  </div>
}