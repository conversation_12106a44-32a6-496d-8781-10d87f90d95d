
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryProtocolSigner } from '../../../graphql';
import { DatePicker } from 'antd';
const { RangePicker} = DatePicker;

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})
 
export default () => {

  const [instance,] = useContext(ApplicationContext)

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  },  {
    title: '签署人名称',
    dataIndex: 'name',
    width: 200
  },{
    title: '签署人类型',
    dataIndex: 'identifyType',
    width: 200,
  }, {
    title: '唯一标识符',
    dataIndex: 'identify',
    width: 200,
  },  {
    title: '签署序列',
    dataIndex: 'seqNum',
    sorter: true
  },{
    title: '协议完成时间',
    dataIndex: 'completeTime',
  }]

  
  const stage = useQueryStage({
    query: queryProtocolSigner,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.protocol.queryProtocolSigners,
    variables: {
      protocolId: id
    }
  })

  

  const buttons = <Button.Group>
    
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage} 
      toolbar={buttons}
    />
  </div>
}