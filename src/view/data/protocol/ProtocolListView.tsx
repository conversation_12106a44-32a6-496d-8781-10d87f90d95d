
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryProtocols } from '../../../graphql';
import { DatePicker } from 'antd';
import { ProtocolStatusSelect ,SignWaySelect} from '../../../component/selects';
import { format } from 'path';
const { RangePicker} = DatePicker;

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})


const BusinessTypeField = withForm(Input, {
  label: '业务类型',
  name: 'businessType',
})
const ProtocolStatusField = withForm(ProtocolStatusSelect, {
  label: '协议状态',
  name: 'status',
})

const MerchantIdField = withForm(Input, {
  label: '商户ID',
  name: 'merchantId',
})

const TenantIdTypeField = withForm(Input, {
  label: '租户ID',
  name: 'tenantId',
})
const SignwayField = withForm(SignWaySelect, {
  label: '签约方式',
  name: 'signWay',
})

const ValidityStartField = withForm(DatePicker, {
  label: '有效开始时间',
  name: 'validityStart'
})


const ValidityEndField = withForm(DatePicker,  {
  label: '有效截止时间',
  name: 'validityEnd',
})

// const ValidityDateField = withForm(RangePicker, {
//   label: '有效时间范围',
//   name: 'validityTime',
  
// })

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  },{
    node: () => <BusinessTypeField />
  },{
    node: () => <ProtocolStatusField />
  },{
    node: () => <MerchantIdField />
  },{
    node: () => <TenantIdTypeField />
  },{
    node: () => <SignwayField />
  },{
    node: () => <ValidityStartField  allowClear showTime autoComplete="OFF" />
  },{
    node: () => <ValidityEndField  allowClear showTime autoComplete="OFF" />
  }]

  const columns: DataTableColumnProps = [{
    title: '协议ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '协议名称',
    dataIndex: 'protocolName',
    width: 200,
  }, {
    title: '业务类型',
    dataIndex: 'businessType',
    width: 90
  }, {
    title: '协议状态',
    dataIndex: 'status',
    width: 150
  }, {
    title: '商户ID',
    dataIndex: 'merchantId',
  }, {
    title: '租户ID',
    dataIndex: 'tenantId',
  },  {
    title: '合同ID',
    dataIndex: 'contractId',
  }, {
    title: '签署人数量',
    dataIndex: 'signCount',
  },{
    title: '有效开始时间',
    dataIndex: 'validityStart',
  }, {
    title: '有效截止时间',
    dataIndex: 'validityEnd',
  }, {
    title: '协议创建时间',
    dataIndex: 'protocolCreateTime',
    sorter: true
  }, {
    title: '协议完成时间',
    dataIndex: 'completeTime',
    sorter: true
  },{
    title: '签约方式',
    dataIndex: 'signWay',
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: queryProtocols,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.protocol.queryProtocols
  })

  const opers: DataTableOperation[] = [

  //   {
  //   title: '详情',
  //   icon: <FaIcon name="pencil-square-o" />,
  //   action: (record) => {
  //     message.warn("暂时无法查看详情")
  //   }
  // }, 
  {
    title: '签署人列表',
    icon: <FaIcon name="bars" />,
    action: (record) => {
      instance.history.push(`/protocol/signerList/${record.id}`)
    }
  }, {
    title: '协议附件列表',
    icon: <FaIcon name="download-data" />,
    action: (record) => {
      instance.history.push(`/protocol/attachmentList/${record.id}`)
    }
  }]

  const buttons = <Button.Group>
    
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}