
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import {queryAgentInfos} from '../../../graphql';
 

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100
  }, {
    title: '名称',
    dataIndex: 'name',
  }, {
    title: '简介',
    dataIndex: 'introduction',
  }, {
    title: '图片id',
    dataIndex: 'archiveId',
  }, {
    title: '是否展示',
    dataIndex: 'show',
    render: (v) =>  v == true ? "是" : "否"
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: queryAgentInfos,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.agentInfo.agentInfos
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/agent/info/edit/${record.id}`)
    }
  }]


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/agent/info/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}