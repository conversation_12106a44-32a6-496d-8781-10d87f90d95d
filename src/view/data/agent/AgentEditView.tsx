import React, {useContext, useState} from 'react';

import {Button, Form, Input, Upload} from 'antd';
import {ApolloSimpleFormStage, ApplicationContext, withForm} from '@lanmaoly/admin-framework';
import {queryAgentInfo, updateAgentInfo} from '../../../graphql';
import {SwitchField} from "../../../component/SwitchField";
import {enviroment} from "../../../env";

const uploadUri = enviroment.domain+"/api/archive/upload";

const SystemCodeField = withForm(Input, {
    label: "名称",
    name: ["name"],
    rules: [{required: true, message: "请输入名称"}]
});

const SystemNameField = withForm(Input, {
    label: "简介",
    name: ["introduction"],
    rules: [{required: true, message: "请输入简介"}]
});

const ShowField = withForm(SwitchField, {
    label: "是否展示",
    name: ["show"],
});

const BasicInfo = () => {

    const [, option] = useContext(ApplicationContext);
    const id = option.match!.params.id;
    const [fid, setFid] = useState("");
    const [imgUrl, setImgUrl] = useState("");
    const [actionUrl, setActionUrl] = useState(uploadUri);

    return <ApolloSimpleFormStage
        id={id}
        query={queryAgentInfo}
        mutation={updateAgentInfo}
        dataPath={(data) => {
            const smsConfig = data.agentInfo.agentInfo;
            setImgUrl(smsConfig.archiveUrl);
            if (id != null) {
                setActionUrl(uploadUri);
            }
            return {
                ...smsConfig
            };
        }}
        onSubmit={(values, mutation) => {
            mutation({
                variables: {
                    ...values,
                    id,
                    archiveId: fid,
                    show: values.show == null ? false : values.show
                }
            })
        }}>
        <SystemCodeField required/>
        <SystemNameField required/>
        <ShowField/>

        <Form.Item label="上传图片">
            <Upload name="file" action={actionUrl} onChange={(info) => {
                if (info.file.status == "done") {
                    console.log(info.file.response);
                    const fid = info.file.response.data.archiveId;
                    setFid(fid);
                    setImgUrl(info.file.response.data.url)
                }
            }}>
                <Button>选择文件</Button>
            </Upload>
            <img src={imgUrl} width={100} height={100}/>
        </Form.Item>

    </ApolloSimpleFormStage>
};

export default () => {
    return <BasicInfo/>
}
