
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryAuthorizationRecords} from '../../../graphql';

import { DatePicker } from 'antd';
const { RangePicker} = DatePicker;

const ResourceOwner = withForm(Input, {
  label: '',
  name: 'keywords',
})
const DateField = withForm(RangePicker, {
  label: '时间范围',
  name: 'createTime',
})

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [
      {
          node: () => <ResourceOwner/>
      },{
        node: () => <DateField allowClear showTime autoComplete="OFF"/>
      }
  ]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100,
  }, {
    title: '拥有着类型',
    dataIndex: 'ownerType',
    width: 100,
  }, {
    title: '资源所有者',
    dataIndex: 'resourceOwner',
    width: 90
  }, {
    title: '目标类型',
    dataIndex: 'targetType',
    width: 90
  }, {
    title: '目标',
    dataIndex: 'target',
    width: 90
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  }, {
    title: '修改时间',
    dataIndex: 'modifyTime',
    width: 180
  }, {
    title: '权限列表',
    dataIndex: 'restrict',
    render: (value) => value?.map(o => o).join(" , ")??""
  }]

  const stage = useQueryStage({
    query: queryAuthorizationRecords,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.authz.authorizationRecords
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/authz/authorizationrecord/edit/${record.id}`)
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/authz/authorizationrecord/add')
    }}>新增</Button>
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}