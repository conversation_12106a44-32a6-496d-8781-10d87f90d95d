
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryAuthorizationRecord, updateAuthorizationRecord } from '../../../graphql';



const OwnerTypeField = withForm(Input, {
  label: "拥有着类型",
  name: ["ownerType"],
  rules: [{ required: true, message: "请输入拥有者类型" }]
})
const ResourceOwnerField = withForm(Input, {
  label: "资源所有者",
  name: ["resourceOwner"],
})
const TargetTypeField = withForm(Input, {
  label: "目标类型",
  name: ["targetType"],
})
const TargetField = withForm(Input, {
  label: "目标",
  name: ["target"],
})

const RestrictField = withForm(Input, {
  label: "权限列表",
  name: ["restrict"],
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryAuthorizationRecord}
    mutation={updateAuthorizationRecord}
    dataPath={(data) => data.authz.authorizationRecord}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
        }
      })
    }}>
    <OwnerTypeField />
    <ResourceOwnerField />
    <TargetTypeField />
    <TargetField />
    <RestrictField/>
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}