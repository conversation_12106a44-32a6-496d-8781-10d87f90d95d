
import React, { useContext } from 'react';

import { Input } from 'antd';
import { ApplicationContext, withForm, ApolloSimpleFormStage } from '@lanmaoly/admin-framework';
import { queryTenantCustomer, updateTenantCustomer } from '../../../graphql';
import { SwitchField } from '../../../component/SwitchField';


const TenantIdField = withForm(Input, {
  label: "租户ID",
  name: ["tenantId"],
  rules: [{ required: true, message: "请输入租户ID" }]
})
 

const CustomerMerchantIdField = withForm(Input, {
  label: "客户商户ID",
  name: ["customerMerchantId"],
  rules: [{ required: true, message: "请输入客户经理ID" }]
}) 

const StatusField = withForm(SwitchField, {
  label: "状态",
  name: ["status"]
})

const BasicInfo = () => {

  const [, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  return <ApolloSimpleFormStage
    id={id}
    query={queryTenantCustomer}
    mutation={updateTenantCustomer}
    dataPath={(data) => {
      const tenantCustomerConfig = data.authz.tenantCustomer
      return {
        ...tenantCustomerConfig,
        status: tenantCustomerConfig.status == 'DISABLE'? "" : tenantCustomerConfig.status
      }
    }}
    onSubmit={(values, mutation) => {
      mutation({
        variables: {
          ...values,
          id,
          status: values.status == '' ? "DISABLE" : "ENABLE"
        }
      })
    }}>
    <TenantIdField required/> 
    <CustomerMerchantIdField required />
    <StatusField/> 
  </ApolloSimpleFormStage>
}

export default () => {

  return <BasicInfo />
}
