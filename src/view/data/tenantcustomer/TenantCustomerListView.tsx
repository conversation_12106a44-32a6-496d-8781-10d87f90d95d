
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryTenantCustomers } from '../../../graphql';
 

const KeyWordsField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <KeyWordsField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 70 
  }, {
    title: '租户ID',
    dataIndex: 'tenantId',
    width: 200,
  }, {
      title: '客户商户ID',
      dataIndex: 'customerMerchantId',
      sorter: true
  }, {
    title: '状态',
    dataIndex: 'status',
    render: (v) =>  v == "DISABLE" ? "禁用" : "启用", 
    sorter: true
},  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200
  }, {
    title: '修改时间',
    dataIndex: 'modifyTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: queryTenantCustomers,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.authz.tenantCustomers
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/authz/tenantcustomer/edit/${record.id}`)
    }
  }]


 


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/authz/tenantcustomer/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}