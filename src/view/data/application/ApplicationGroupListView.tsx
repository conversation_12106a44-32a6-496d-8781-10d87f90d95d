
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import {queryApplicationGroups} from '../../../graphql';
 

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

    

export default () => {

  const [instance] = useContext(ApplicationContext)

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: 'ID',
    dataIndex: 'id',
    width: 100
  }, {
    title: '组名称',
    dataIndex: 'name',
  }, {
    title: '应用',
    dataIndex: 'applications',
    render: (value) => value?.map(o => o.name).join(" , ")??""
  }, {
    title: '图片id',
    dataIndex: 'archiveId',
  }, {
    title: '是否启用',
    dataIndex: 'enable',
    render: (v) =>  v == true ? "是" : "否"
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]

  const stage = useQueryStage({
    query: queryApplicationGroups,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.applicationGroup.applicationGroups
  })


  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/application/group/edit/${record.id}`)
    }
  }]


  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push('/application/group/add')
    }}>新增</Button>
  </Button.Group>


  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}