
import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { getLocationSearch } from '../utils';
import { enviroment } from '../env';
import { FaIcon } from '@lanmaoly/admin-framework';
import { Form, Button } from 'antd'


export default observer((props: {
  onLogin: (token: string) => void
}) => {
  const { onLogin } = props
  const [login, setLogin] = useState(false)

  const token = getLocationSearch()?.token

  useEffect(() => {
    if (!login) {
      if (token) {
        setTimeout(() => {
          onLogin(token)
        }, 1000)
      }
    }
    setLogin(true)
  }, [token, login, onLogin])

  return <div className="admin-login-view">

    <div className="admin-login-view-panel">
      <div style={{
        display: 'flex',
        justifyContent: 'center'
      }}>
        <FaIcon name="lock" className="admin-login-view-icon" />
      </div>
      <div style={{
        height: 50
      }}></div>
      <Form >
        <Button loading={token != null} onClick={() => {
          const sso = document.location.href;
          const url = enviroment?.bossLogin
          document.location.href = `${url}?redirect=` + encodeURIComponent(sso)
        }} className="admin-login-view-button" type="primary">{
            token ? "正在登录" : "登录"
          }</Button>
      </Form>
    </div >
  </div >
})