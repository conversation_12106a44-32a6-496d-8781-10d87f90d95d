import React, { useState,useContext } from 'react';
import { Form, Input, message, Radio, Button, Select } from 'antd';
import { ApolloSimpleFormStage, ApplicationContext, withForm } from '@lanmaoly/admin-framework';
import { enviroment } from "../../env";
const { Option } = Select;

// import { useHistory } from 'react-router-dom';

const SetAuthCacheForm = () => {
  const [isUpdate, setIsUpdate] = useState(true);
  const [name, setName] = useState('');
  const [idNo, setIdNo] = useState('');
  const [phone, setPhone] = useState('');
  const [card, setCard] = useState('');
  const [mode, setMode] = useState('');
  const [isPassed, setIsPassed] = useState(1); // 1: 是, 0: 否
  const [instance] = useContext(ApplicationContext);


  const onFinish = (values) => {
    const { mode='', name='', idNo='', card = '', phone = '', isPassed='' } = values;
    const params = new URLSearchParams({
      mode,
      name,
      idNo,
      card,
      phone,
      ok: isPassed ? 'true' : 'false',
    });

    fetch(`${enviroment.domain}/api/auth/setAuthCache?${params.toString()}`, {
      method: 'POST',
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          message.success('设置成功');
          setTimeout(() => {
            window.location.reload()
          }, 2000);
        //   instance.history.push('/realname/cache'); // 提交成功后返回实名认证缓存列表页
        } else {
          message.error(data.message || '设置失败');
        }
      })
      .catch(error => {
        message.error('设置失败，请稍后重试');
        console.error(error);
      });
  };

  return (
    <Form name="setAuthCache" onFinish={onFinish} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} style={{ maxWidth: 600 }}>
      <Form.Item label="实名认证类型" name="mode" rules={[{ required: true, message: '请选择实名认证类型' }]}>
        <Select placeholder="请选择实名认证类型" value={mode} onChange={(e) => setMode(e)}>
          <Option value="ID_NAME">身份证、姓名</Option>
          <Option value="ID_NAME_PHONE">身份证、姓名、手机号</Option>
          <Option value="ID_NAME_CARD">身份证、姓名、卡号</Option>
          <Option value="ID_NAME_PHONE_CARD">身份证、姓名、手机号、卡号</Option>
        </Select>
      </Form.Item>
      <Form.Item label="姓名" name="name" rules={[{ required: true, message: '请输入姓名' }, { pattern: /^[\u4e00-\u9fa5]{2,}$/, message: '请输入正确的姓名' }]}>
        <Input placeholder="请输入姓名" value={name} onChange={(e) => setName(e.target.value)} />
      </Form.Item>
      <Form.Item label="身份证" name="idNo" rules={[{ required: true, message: '请输入身份证号码' }, { pattern: /^\d{17}[\dXx]$/, message: '请输入正确的身份证号码' }]}>
        <Input placeholder="请输入身份证号码" value={idNo} onChange={(e) => setIdNo(e.target.value)} />
      </Form.Item>
      <Form.Item label="手机号" name="phone" rules={[{ required: false, message: '请输入手机号' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }]}>
        <Input placeholder="请输入手机号" value={phone} onChange={(e) => setPhone(e.target.value)} />
      </Form.Item>
      <Form.Item label="卡号" name="card" rules={[{ required: false, message: '请输入卡号' },{ max: 20, message: '卡号长度不能超过20位' }]}>
        <Input placeholder="请输入卡号" maxLength={20} value={card} onChange={(e) => setCard(e.target.value)} />
      </Form.Item>
      <Form.Item label="是否通过认证" name="isPassed" rules={[{ required: true, message: '请选择是否通过认证' }]}>
        <Radio.Group value={isPassed} onChange={(e) => setIsPassed(e.target.value)}>
          <Radio value={1}>是</Radio>
          <Radio value={0}>否</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item>
        <Button style={{marginLeft:'200px'}} type="primary" htmlType="submit">提交</Button>
      </Form.Item>
    </Form>
  );
};

export default SetAuthCacheForm;
