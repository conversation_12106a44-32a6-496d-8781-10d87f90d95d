import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import * as serviceWorker from './serviceWorker';
import 'moment/locale/zh-cn';
import moment from 'moment'
import './assets/css/icon.css'

// mock()

moment.locale('zh-cn');
moment.fn.toJSON = function () { return this.format("YYYY-MM-DDTHH:mm:ss") }

// mobx.configure({ enforceActions: 'observed' })

ReactDOM.render(<App />, document.getElementById('root'));

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
