import { Select } from 'antd';
import React, { useState } from 'react';

export interface SampleCascadeSelectOptions {
    value?: any[]
    onChange?: (value) => void
  }
  

export function SampleCascadeSelect(props: SampleCascadeSelectOptions) {

    const option1 = [{
      label: "A1",
      value: "A1"
    }, {
      label: "A2",
      value: "A2"
    }]
    const option2 = [{
      label: "B1",
      value: "B1"
    }, {
      label: "B2",
      value: "B2"
    }]
  
    const value = props.value??[]
  
    const [option, setOption] = useState(option1 as any)
    const [value1, setValue1] = useState(value[0])
    const [value2, setValue2] = useState(value[1])
  
    return <div>
      <Select style={{
        width: 100
      }} options={[{
        label: "A",
        value: "A"
      }, {
        label: "B",
        value: "B"
      }]} value={value1} onChange={(v) => {
        setValue1(v)
        if (v == 'A') {
          setOption(option1)
          setValue2(undefined)
        } else if (v == 'B') {
          setOption(option2)
          setValue2(undefined)
        }
      }}>
      </Select>
      <Select style={{
        width: 100
      }} options={option} value={value2} onChange={(v) => {
        setValue2(v)
        if (props.onChange) {
          props.onChange([value1, v])
        }
      }}>
      </Select>
    </div>
  }