import React from "react";
import { Select, Radio} from "antd"
import { ApolloRemoteSelect, RemoteSelectProps } from "@lanmaoly/admin-framework";
import {
  queryRoles,
  queryApplications,
  querySmsChannels,
  querySmsTemplates,
  querySystemRole,
  getMerchantUser,
  querySystemEnableRole
} from "../graphql";


export type SelectProps = Omit<RemoteSelectProps, keyof {
  query: any
  optionSelector: any
  labelProps: any
  valueProp: any
}>


export interface MerchantUserRoleSelectProps {
  merchantUserId: any
}

export function createMerchantUserRoleSelect(merchantUserId: any) {
  return () => {
    return <ApolloRemoteSelect
        query={getMerchantUser}
        variables={{
          id: merchantUserId
        }}
        optionSelector={(data) => data.applicationGroup.queryMerchant.roles}
        labelProps="roleName"
        valueProp="id" />
  }
}

export function MerchantUserRoleSelect(props: MerchantUserRoleSelectProps) {
  return <ApolloRemoteSelect
    query={getMerchantUser}
    variables={props.merchantUserId}
    optionSelector={(data) => data.authz.roles.data}
    labelProps="name"
    valueProp="id" {...props} />
}

export function RoleSelect(props: SelectProps) {
  return <ApolloRemoteSelect
      query={queryRoles}
      optionSelector={(data) => data.authz.roles.data}
      labelProps="name"
      valueProp="id" {...props} />
}

export function SystemRoleSelect(props: SelectProps) {
  return <ApolloRemoteSelect
      query={querySystemRole}
      optionSelector={(data) => data.applicationGroup.queryRole.data}
      labelProps="roleName"
      valueProp="id" {...props} />
}

export function SystemEnableRoleSelect(props: SelectProps) {
  return <ApolloRemoteSelect
      query={querySystemEnableRole}
      optionSelector={(data) => data.merchantPrivileges.querySystemEnableRole}
      labelProps="roleName"
      valueProp="id" {...props} />
}


export function ApplicationSelect(props: SelectProps) {
  return <ApolloRemoteSelect
    query={queryApplications}
    optionSelector={(data) => data.applicationGroup.applications.data}
    labelProps="name"
    valueProp="code" {...props} />
}

export function ChannelSelect(props: SelectProps) {
    return <ApolloRemoteSelect
        query={querySmsChannels}
        optionSelector={(data) => data.sms.smsChannels.data}
        labelProps="name"
        valueProp="id" {...props} />
}

export function TemplateSelect(props: SelectProps) {
    return <ApolloRemoteSelect
        query={querySmsTemplates}
        optionSelector={(data) => data.sms.smsTemplates.data}
        labelProps="name"
        valueProp="id" {...props} />
}

export function IsLinkedUpFieldSelect(props: SelectProps) {
  return <Select size="middle"  options={[{
    label: "全部",
    value: ""
  }, {
    label: "是",
    value: "true"
  }, {
    label: "否",
    value: "false"
  }]} {...props}>
  </Select>
}



export function  ProtocolStatusSelect(props: SelectProps) {
  return <Select options={[{
    label: "全部",
    value: ""
  }, {
    label: "正常",
    value: "NORMAL"
  }, {
    label: "过期",
    value: "EXPIRE"
  }, {
    label: "作废",
    value: "CANCEL"
  }]} {...props}>
  </Select>
}




export function  SignWaySelect(props: SelectProps) {
  return <Select allowClear options={[{
    label: "全部",
    value: ""
  },{
    label: "纸质签约",
    value: "OFFLINE"
  }, {
    label: "阿拉钉电子签约",
    value: "OLADING"
  }]} {...props}>
  </Select>
}

export function MerchantStatusRodio(props: SelectProps) {
  return <Select size="middle" 
  style={{width: '100px'}}
  options={[{
    label: "全部",
    value: ""
  }, {
    label: "配置中",
    value: "CONFIG"
  }, {
    label: "已完成",
    value: "CONFIG_SUCCESS"
  }]} {...props}>
  </Select>



}