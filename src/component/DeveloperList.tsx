
import { Input, Button, message } from 'antd';
import React, { useContext } from 'react';
import { QueryStage, QueryStageFilterOption, withForm, DataTableColumnProps, DataTableOperation, FaIcon, ApplicationContext, useQueryStage } from '@lanmaoly/admin-framework';
import { queryDevelopers } from '../graphql';

const NameField = withForm(Input, {
  label: '关键字',
  name: 'keywords',
})

export default (props: {
  tenant: string
}) => {

  const [instance, option] = useContext(ApplicationContext)
  const id = option.match!.params.id

  const filters: QueryStageFilterOption[] = [{
    node: () => <NameField />
  }]

  const columns: DataTableColumnProps = [{
    title: '开发者编号',
    dataIndex: 'id',
    width: 300
  }, {
    title: '私钥',
    dataIndex: 'privateKey',
    width: 300
  }, {
    title: '回调地址',
    dataIndex: 'callbackUrl',
    width: 300
  }, {
    title: '标志',
    dataIndex: 'flags',
    render: (v) => v == null ? "" : v.join(", ")
  }]

  const stage = useQueryStage({
    query: queryDevelopers,
    columns,
    rowKey: "id",
    filters,
    dataPath: (data) => data.authz.developers,
    variables: {
      tenant: props.tenant
    }
  })

  const opers: DataTableOperation[] = [{
    title: '编辑',
    icon: <FaIcon name="pencil-square-o" />,
    action: (record) => {
      instance.history.push(`/authz/developer/edit/${record.id}`)
    }
  }, {
    title: '删除',
    icon: <FaIcon name="delete-icon" />,
    confirmText: '确认要删除?',
    action: () => {
      message.warn("暂不允许删除")
    }
  }]

  const buttons = <Button.Group>
    <Button type="primary" onClick={() => {
      instance.history.push(`/authz/tenant/${id}/developer/add`)
      // apolloClient.mutate({
      //   mutation: updateDeveloper,
      //   variables: {
      //     tenant: id
      //   }
      // }).then(() => {
      //   message.success("创建成功")
      //   stage.refresh()
      // })
    }}>新增</Button>
  </Button.Group>

  return <div>
    <QueryStage
      instance={stage}
      operation={opers}
      toolbar={buttons}
    />
  </div>
}