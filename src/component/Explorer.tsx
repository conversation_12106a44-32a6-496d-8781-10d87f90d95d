import React, {<PERSON>actN<PERSON>, useState} from 'react';
import {observer} from 'mobx-react';
import {Col, Row, Table, Tree,Space,Tooltip,Button,message,Popconfirm} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import _ from 'lodash';
import {ColumnsType} from "antd/lib/table";
import {FaIcon} from '@lanmaoly/admin-framework';
import {apolloClient, deletePrivilege, deletePrivilegeGroup} from "../graphql";

class TreeWalker {

    childrenGetter: (node: any) => any[]
    titleGetter: (node: any) => string
    keyGetter: (node: any) => any

    constructor(props: {
        childrenGetter: (node) => any[]
        titleGetter: (node) => string
        keyGetter: (node) => any
    }) {
        this.childrenGetter = props.childrenGetter
        this.titleGetter = props.titleGetter
        this.keyGetter = props.keyGetter
    }

    find(root: any, key: string, callback: (node: any, parent: any, index: number) => void) {
        const children = this.childrenGetter(root)
        if (!children || children.length == 0) {
            return false
        }
        for (let i = 0; i < children.length; i++) {
            const child = children[i]
            if (child.key === key) {
                callback(child, root, i)
                return true
            }
            if (this.find(child, key, callback)) {
                return true
            }
        }
        return false
    }

    move(tree: any, node: string, parent: string, index: number) {

        let t
        this.find(tree, node, (n, p, i) => {
            t = n
            this.childrenGetter(p).splice(i, 1)
        })

        this.find(tree, parent, (n, p, i) => {
            const children = this.childrenGetter(n)
            children.splice(index, 0, t)
        })
    }
}

function confirm(e) {
    console.log(e);
    message.success('Click on Yes');
}

function useExplorer(props: {
    data: any[]
    childrenGetter: (node: any) => any[]
    titleGetter: (node: any) => string
    keyGetter: (node: any) => any
}) {

    const walk = (n) => {
        const children = props.childrenGetter(n)
        const title = props.titleGetter(n)
        const key = props.keyGetter(n)

        const t = children.map(o => walk(o))
        return {
            children: t,
            title,
            key,
            data: n
        }
    }

    // const ttt = props.data.map(o => walk(o))
    // const [data, setData] = useState(() => ({
    //   key: "",
    //   title: "",
    //   root: true,
    //   children: ttt
    // } as any))

    const data = {
        key: "",
        title: "",
        root: true,
        children: props.data.map(o => walk(o))
    }

    const [items, setItems] = useState([] as any[])
    const [selectedNode, setSelectNode] = useState(undefined as any)

    const walker = new TreeWalker(props)

    const find = (key: string, callback: (node: any, parent: any, index: number) => void) => {
        walker.find(data, key, callback)
    }

    const move = (node: string, parent: string, index: number) => {
        // walker.move(data, node, parent, index)
        // setData(_.cloneDeep(data))
    }

    return {
        find,
        move,
        selectNode(node: string) {
            find(node, (n) => setSelectNode(n))
        },
        data() {
            return data.children
        },
        setItems(v: any[]) {
            setItems(v)
        },
        items,
        selectedNode
    }
}

type ExplorerModel = ReturnType<typeof useExplorer>

interface ExplorerProps {
    data: any[]
    columns: ColumnsType<any>
    rowKey?: string
    draggable?: boolean
    model?: ExplorerModel
    itemsLoader?: (node: any) => Promise<any[]>
    childrenGetter?: (node: any) => any[]
    titleGetter?: (node: any) => string
    keyGetter?: (node: any) => any
}


const rendertitle = (nodeData) => {
    if(nodeData.children.length == 0 &&  nodeData.data.privileges && nodeData.data.privileges.length == 0){
        return <div style={{width:"100%",position:"relative"}}>
            <div>{nodeData.title}</div>
            <div style={{position:"absolute",right:0,top: "0px",width: "14px",height: "14px"}} >
                <Popconfirm
                    title="你确定要删除吗?"
                    okText="确认"
                    onConfirm={(values) => {
                        apolloClient.mutate({
                            mutation:deletePrivilegeGroup,
                            variables: {
                                id: nodeData.key
                            }
                        }).then((reponse:any) => {
                            message.success("删除成功,刷新页面生效")
                            return rendertitle(nodeData)
                        })
                    }}
                    cancelText="取消"
                ><a href="#"><FaIcon name="trash-o"  /></a>

                </Popconfirm>
            </div>
        </div>
    }else {
        return <div>
            <div>{nodeData.title}</div>
        </div>
    }

}

export default observer((props: ExplorerProps) => {

    const data = props.data
    const childrenGetter = props.childrenGetter ?? (n => n.children ?? []) as (node: any) => any[]
    const titleGetter = props.titleGetter ?? (n => n.title) as (node: any) => string
    const keyGetter = props.keyGetter ?? (n => n.key) as (node: any) => any

    const m = useExplorer({
        data,
        childrenGetter,
        titleGetter,
        keyGetter
    })

    return <div>
        <Row>
            <Col span={8}>
                <Tree showLine draggable={props.draggable} blockNode treeData={m.data()}  style={{
                    height: 600,
                    overflow: "auto"
                }} onSelect={(selectedKeys, info) => {

                    if (props.itemsLoader) {
                        const a = info.selectedNodes[0] as any
                        if(a && a.data){
                            props.itemsLoader(a.data).then(r => {
                                m.setItems(r)
                            })
                        }
                    }
                }} onDragEnter={(info) => {

                }} onDrop={info => {
                    if (!info.dropToGap) {
                        m.move(info.dragNode.key as string, info.node.key as string, 0)
                    } else {
                        // 移动到info.node的同级
                        let parent, index
                        m.find(info.node.key as string, (n, p, i) => {
                            parent = p
                            index = i
                        })
                        if (!parent.root) {
                            m.move(info.dragNode.key as string, parent.key, index + 1)
                        }
                    }
                }}
                      titleRender={rendertitle}
                />

            </Col>
            <Col span={16}>
                <Table columns={props.columns} dataSource={m.items} size="small" rowKey={props.rowKey ?? "id"}
                       pagination={false}>
                </Table>
            </Col>
        </Row>

    </div>
})