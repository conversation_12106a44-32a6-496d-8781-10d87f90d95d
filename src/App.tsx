import React, { useState, useEffect } from 'react';
import { ApolloProvider } from '@apollo/react-hooks';
import { Spin } from 'antd'
import { Application } from "@lanmaoly/admin-framework"
import './app.less';
import { Router, Switch, Route, Redirect } from 'react-router';
import { apolloClient, bossAuthorize } from './graphql';
import { Profile } from './types';
import { loadProfile } from './loader';
import { config } from './config';
import LoginView from './view/LoginView';
import { globalHistory, logout, getToken } from './utils';

function login(token: string): Promise<Profile> {
  return apolloClient.mutate({
    mutation: bossAuthorize,
    variables: {
      token
    }
  }).then(r => {
    const token = r.data.bossAuthorize
    sessionStorage.setItem("token", token)
    return true
  }).then(() => loadProfile())
}





const Content = (props: {
  profile?: Profile
}) => {

  const [profile, setProfile] = useState(props.profile)

  useEffect(() => {
    if (!profile) {
      loadProfile().then(r => {
        setProfile(r)
      })
    }
  }, [profile])

  if (!profile) {
    return <Spin spinning={true} />
  }

  return <Application
    menus={config.menus}
    viewFactory={config.viewFactory}
    authorities={profile.authorities}
    history={globalHistory}
    onLogout={async instance => {
      logout()
      instance.history.push("/login")
    }} />
}


const App: React.FC = () => {

  const [profile, setProfile] = useState(null as any)
  // const baseUrl: string = "/gd/hrsaas/platform-boss";
  const baseUrl: string = "";

  return (
    <ApolloProvider client={apolloClient}>
      <Router history={globalHistory}>
        <Switch>
          <Route exact path={baseUrl + "/login"}>
            {/* <LoginView history={globalHistory} onLogin={async (user, password) => {
              return login(user, password).then(profile => {
                setProfile(profile)
                return true
              })
            }} /> */}
            <LoginView onLogin={(token) => {
              logout()
              login(token).then(profile => {

                setProfile(profile)
                globalHistory.push(baseUrl + "/dashboard")
                return true
              }).catch(() => {
                globalHistory.push(baseUrl + "/login")
              })
            }} />
          </Route>
          <Route render={() => {
            if (getToken()) {
              return <Content profile={profile} />
            } else {
              return <Redirect to={baseUrl + "/login"}></Redirect>
            }
          }}></Route>
        </Switch>
      </Router>
    </ApolloProvider >
  );
}

export default App;
