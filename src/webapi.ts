
import { AxiosRequestConfig } from "axios"


function baseReq(option: AxiosRequestConfig): Promise<any> {
  // return request({
  //   method: "POST",
  //   ...option
  // }).then(resp => {
  //   if (!resp.success) {
  //     throw new Error(resp.message)
  //   }
  //   return resp.data
  // })
  return Promise.resolve({})
}

class WebApi {

  async login(name: string, password: string) {
    return baseReq({
      url: "/api/login",
      data: {
        name: name,
        password: password
      }
    })
  }

  async profile(token: string) {
    return baseReq({
      url: "/api/profile",
      data: {
        token: token
      }
    })
  }

  async applicationAdd(values: any) {
    return baseReq({
      url: "/api/data/application/add",
      data: values
    })
  }

  async applicationUpdate(values: any) {
    return baseReq({
      url: "/api/data/application/update",
      data: values
    })
  }

  async applicationDetail(id: number) {
    return baseReq({
      url: "/api/data/application/detail",
      data: {
        id: id
      }
    })
  }

  async hostAdd(values: any) {
    return baseReq({
      url: "/api/data/host/add",
      data: values
    })
  }

  async hostUpdate(values: any) {
    return baseReq({
      url: "/api/data/host/update",
      data: values
    })
  }

  async hostDetail(id: number) {
    return baseReq({
      url: "/api/data/host/detail",
      data: {
        id: id
      }
    })
  }

  async deploymentAdd(values: any) {
    const { hosts } = values
    return baseReq({
      url: "/api/data/deployment/add",
      data: {
        ...values,
        hosts: hosts.split(",").flatMap(o => {
          if (o.trim() === "") {
            return []
          } else {
            return [o.trim()]
          }
        })
      }
    })
  }

  async deploymentUpdate(values: any) {
    const { hosts } = values
    return baseReq({
      url: "/api/data/deployment/update",
      data: {
        ...values,
        hosts: hosts.split(",").flatMap(o => {
          if (o.trim() === "") {
            return []
          } else {
            return [o.trim()]
          }
        })
      }
    })
  }

  async deploymentDetail(id: number) {
    return baseReq({
      url: "/api/data/deployment/detail",
      data: {
        id: id
      }
    }).then(resp => ({
      ...resp.info,
      hosts: resp.hosts.map(o => o.address).join(","),
      users: resp.users
    }))
  }

  async userAdd(values: any) {
    return baseReq({
      url: "/api/data/user/add",
      data: values
    })
  }

  async userUpdate(values: any) {
    return baseReq({
      url: "/api/data/user/update",
      data: values
    })
  }

  async userDetail(id: number) {
    return baseReq({
      url: "/api/data/user/detail",
      data: {
        id: id
      }
    })
  }

  async alertRuleAdd(values: any) {
    return baseReq({
      url: "/api/alert/rule/add",
      data: values
    })
  }

  async alertRuleUpdate(values: any) {
    return baseReq({
      url: "/api/alert/rule/update",
      data: values
    })
  }


  async alertRuleDetail(id: number) {
    return baseReq({
      url: "/api/alert/rule/detail",
      data: {
        id: id
      }
    })
  }

  async alertRuleDelete(id: number) {
    return baseReq({
      url: "/api/alert/rule/delete",
      data: {
        id: id
      }
    })
  }

  async alertReceiverGroupAdd(values: any) {
    return baseReq({
      url: "/api/alert/receiver-group/add",
      data: values
    })
  }

  async alertReceiverGroupUpdate(values: any) {
    return baseReq({
      url: "/api/alert/receiver-group/update",
      data: values
    })
  }

  async alertReceiverGroupDetail(id: number) {
    return baseReq({
      url: "/api/alert/receiver-group/detail",
      data: {
        id: id
      }
    })
  }

  async dataSourceAdd(values: any) {
    return baseReq({
      url: "/api/data/data-source/add",
      data: values
    })
  }

  async dataSourceUpdate(values: any) {
    return baseReq({
      url: "/api/data/data-source/update",
      data: values
    })
  }

  async dataSourceDetail(id: number) {
    return baseReq({
      url: "/api/data/data-source/detail",
      data: {
        id: id
      }
    })
  }
}

export const api = new WebApi()