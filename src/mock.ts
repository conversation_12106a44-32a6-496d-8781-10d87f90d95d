import mockjs from 'mockjs';
// import fetchMock from 'fetch-mock'
import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'

export function delay<T>(duration: number, v: T): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(v)
    }, duration)
  })
}

export default () => {

  const mock = new MockAdapter(axios);
  
  mock.onPost(/^\/api\/data.*/).passThrough()
  mock.onPost(/^\/api\/operation.*/).passThrough()
  mock.onPost('/api/login').passThrough()

}